
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, Users, ArrowRightLeft } from 'lucide-react';

export const MarketVisualization = React.memo(function MarketVisualization() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center">How Stock Markets Connect Businesses and Investors</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          {/* Companies Section */}
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 bg-blue-100 rounded-full">
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-center">
              <h4 className="font-semibold">Companies</h4>
              <p className="text-sm text-muted-foreground">Need capital to grow</p>
              <div className="mt-2 space-y-1">
                <Badge variant="outline" className="text-xs">Apple</Badge>
                <Badge variant="outline" className="text-xs">Microsoft</Badge>
                <Badge variant="outline" className="text-xs">Amazon</Badge>
              </div>
            </div>
          </div>

          {/* Exchange Arrow */}
          <div className="flex flex-col items-center space-y-2">
            <ArrowRightLeft className="h-6 w-6 text-gray-400" />
            <div className="text-center">
              <Badge className="bg-green-100 text-green-800">Stock Exchange</Badge>
              <p className="text-xs text-muted-foreground mt-1">NYSE, NASDAQ</p>
            </div>
          </div>

          {/* Investors Section */}
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 bg-green-100 rounded-full">
              <Users className="h-8 w-8 text-green-600" />
            </div>
            <div className="text-center">
              <h4 className="font-semibold">Investors</h4>
              <p className="text-sm text-muted-foreground">Want to grow wealth</p>
              <div className="mt-2 space-y-1">
                <Badge variant="outline" className="text-xs">Individuals</Badge>
                <Badge variant="outline" className="text-xs">Institutions</Badge>
                <Badge variant="outline" className="text-xs">Funds</Badge>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-center text-gray-600">
            <strong>The Exchange:</strong> When companies issue shares (IPO), investors can buy them. 
            Later, investors can trade these shares with each other through the stock exchange.
          </p>
        </div>
      </CardContent>
    </Card>
  );
});
