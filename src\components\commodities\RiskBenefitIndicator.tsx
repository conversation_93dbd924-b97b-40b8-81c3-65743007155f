
import { cn } from "@/lib/utils";

interface RiskBenefitIndicatorProps {
  ratio: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export function RiskBenefitIndicator({ 
  ratio, 
  size = 'md',
  showLabel = true 
}: RiskBenefitIndicatorProps) {
  // Normalize ratio to a scale that makes sense visually
  // Values typically range from ~1 (poor) to ~6+ (excellent)
  const getColorClass = () => {
    if (ratio >= 5) return "bg-success";
    if (ratio >= 3.5) return "bg-warning";
    if (ratio >= 2) return "bg-amber-500";
    return "bg-danger";
  };
  
  const getLabel = () => {
    if (ratio >= 5) return "Excellent";
    if (ratio >= 3.5) return "Good";
    if (ratio >= 2) return "Fair";
    return "Poor";
  };
  
  const heightClass = {
    'sm': 'h-1',
    'md': 'h-2',
    'lg': 'h-3'
  }[size];
  
  // Cap display at 7 for visualization purposes
  const displayRatio = Math.min(ratio, 7);
  const percentage = (displayRatio / 7) * 100;

  return (
    <div className="space-y-1">
      {showLabel && (
        <div className="flex justify-between text-xs">
          <span>Risk/Benefit</span>
          <span className="font-medium">{ratio.toFixed(2)}</span>
        </div>
      )}
      <div className={cn("w-full bg-muted rounded-full overflow-hidden", heightClass)}>
        <div 
          className={cn("h-full rounded-full transition-all", getColorClass())}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showLabel && (
        <div className="flex justify-end">
          <span className="text-xs text-muted-foreground">{getLabel()}</span>
        </div>
      )}
    </div>
  );
}
