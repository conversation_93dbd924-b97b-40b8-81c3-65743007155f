
import { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { ProspectiveStock } from '@/types/prospectiveStocks';
import { formatCurrency } from '@/utils/formatters';
import { ScoreIndicator } from './ScoreIndicator';
import { ChevronDown, Globe } from 'lucide-react';

interface ProspectiveStockTableProps {
  stocks: ProspectiveStock[];
  onStockSelect: (stock: ProspectiveStock) => void;
}

type SortField = 'overallScore' | 'growthScore' | 'valueScore' | 'momentumScore' | 'marketCap' | 'price' | 'pe' | 'dividend';

export function ProspectiveStockTable({ stocks, onStockSelect }: ProspectiveStockTableProps) {
  const [sortField, setSortField] = useState<SortField>('overallScore');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  const handleSortChange = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  const sortedStocks = [...stocks].sort((a, b) => {
    const multiplier = sortDirection === 'asc' ? 1 : -1;
    return (a[sortField] - b[sortField]) * multiplier;
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">Rank</TableHead>
            <TableHead>Symbol</TableHead>
            <TableHead className="min-w-[180px]">Name</TableHead>
            <TableHead className="hidden md:table-cell">Region</TableHead>
            <TableHead className="hidden lg:table-cell">Sector</TableHead>
            <TableHead className="text-right">Price</TableHead>
            <TableHead className="hidden md:table-cell text-right">
              <Button 
                variant="ghost" 
                onClick={() => handleSortChange('marketCap')} 
                className="h-6 px-1 font-medium"
              >
                Market Cap {sortField === 'marketCap' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
            </TableHead>
            <TableHead className="hidden md:table-cell text-right">
              <Button 
                variant="ghost" 
                onClick={() => handleSortChange('pe')} 
                className="h-6 px-1 font-medium"
              >
                P/E {sortField === 'pe' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
            </TableHead>
            <TableHead className="hidden lg:table-cell text-right">
              <Button 
                variant="ghost" 
                onClick={() => handleSortChange('dividend')} 
                className="h-6 px-1 font-medium"
              >
                Dividend {sortField === 'dividend' && (sortDirection === 'asc' ? '↑' : '↓')}
              </Button>
            </TableHead>
            <TableHead className="text-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-6 px-1 font-medium">
                    Score {sortField.includes('Score') && (sortDirection === 'asc' ? '↑' : '↓')}
                    <ChevronDown className="h-3.5 w-3.5 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleSortChange('overallScore')}>
                    Overall {sortField === 'overallScore' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange('growthScore')}>
                    Growth {sortField === 'growthScore' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange('valueScore')}>
                    Value {sortField === 'valueScore' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSortChange('momentumScore')}>
                    Momentum {sortField === 'momentumScore' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedStocks.map((stock, index) => (
            <TableRow 
              key={stock.symbol} 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => onStockSelect(stock)}
            >
              <TableCell className="font-medium">{index + 1}</TableCell>
              <TableCell>
                <div className="font-medium">{stock.symbol}</div>
              </TableCell>
              <TableCell>{stock.name}</TableCell>
              <TableCell className="hidden md:table-cell">
                <div className="flex items-center gap-1">
                  <Globe className="h-3.5 w-3.5 text-muted-foreground" />
                  <span>{stock.region}</span>
                </div>
              </TableCell>
              <TableCell className="hidden lg:table-cell">
                <Badge variant="outline" className="font-normal">
                  {stock.sector}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <div className="font-medium">${stock.price.toFixed(2)}</div>
                <div className={`text-xs ${stock.change >= 0 ? 'text-success' : 'text-danger'}`}>
                  {stock.change >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                </div>
              </TableCell>
              <TableCell className="hidden md:table-cell text-right">
                {formatCurrency(stock.marketCap)}
              </TableCell>
              <TableCell className="hidden md:table-cell text-right">
                {stock.pe.toFixed(1)}
              </TableCell>
              <TableCell className="hidden lg:table-cell text-right">
                {(stock.dividend / stock.price * 100).toFixed(2)}%
              </TableCell>
              <TableCell>
                <div className="flex items-center justify-between">
                  <div className="w-16 mr-2">
                    <ScoreIndicator 
                      score={stock.overallScore} 
                      label="Overall" 
                      showLabel={false} 
                      size="sm"
                    />
                  </div>
                  <span className="font-medium">{stock.overallScore}</span>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

export type { ProspectiveStockTableProps };
