
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowDown, ArrowUp } from 'lucide-react';

export const TradingFlowDiagram = React.memo(function TradingFlowDiagram() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center">How a Trade Happens</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Step 1 */}
          <div className="text-center space-y-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
              <span className="font-bold text-blue-600">1</span>
            </div>
            <div>
              <h4 className="font-semibold">Place Order</h4>
              <p className="text-sm text-muted-foreground">Investor decides to buy/sell</p>
              <div className="mt-2">
                <Badge variant="outline" className="text-xs">Market Order</Badge>
                <Badge variant="outline" className="text-xs ml-1">Limit Order</Badge>
              </div>
            </div>
          </div>

          <div className="hidden md:flex items-center justify-center">
            <ArrowDown className="h-6 w-6 text-gray-400" />
          </div>

          {/* Step 2 */}
          <div className="text-center space-y-4">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <span className="font-bold text-green-600">2</span>
            </div>
            <div>
              <h4 className="font-semibold">Order Matching</h4>
              <p className="text-sm text-muted-foreground">Exchange finds matching orders</p>
              <div className="mt-2 space-y-1">
                <div className="text-xs">
                  <span className="text-green-600">Bid: $100</span>
                  <span className="mx-2">⟷</span>
                  <span className="text-red-600">Ask: $100</span>
                </div>
              </div>
            </div>
          </div>

          <div className="hidden md:flex items-center justify-center">
            <ArrowDown className="h-6 w-6 text-gray-400" />
          </div>

          {/* Step 3 */}
          <div className="text-center space-y-4">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
              <span className="font-bold text-purple-600">3</span>
            </div>
            <div>
              <h4 className="font-semibold">Trade Executed</h4>
              <p className="text-sm text-muted-foreground">Ownership transfers</p>
              <div className="mt-2">
                <Badge variant="outline" className="text-xs">Settlement: T+2</Badge>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-start gap-2">
            <ArrowUp className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-800">Did you know?</p>
              <p className="text-xs text-yellow-700">
                Most trades happen in milliseconds thanks to electronic trading systems. 
                The "T+2" means the actual transfer of money and shares happens 2 business days after the trade.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
