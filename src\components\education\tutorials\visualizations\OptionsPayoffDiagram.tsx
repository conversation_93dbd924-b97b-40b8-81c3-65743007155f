
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface PayoffData {
  stockPrice: number;
  callValue: number;
  putValue: number;
  callProfit: number;
  putProfit: number;
}

export const OptionsPayoffDiagram = React.memo(function OptionsPayoffDiagram() {
  const [strikePrice, setStrikePrice] = useState([100]);
  const [callPremium, setCallPremium] = useState([5]);
  const [putPremium, setPutPremium] = useState([4]);
  const [selectedOption, setSelectedOption] = useState<'call' | 'put'>('call');

  const generatePayoffData = (): PayoffData[] => {
    const data: PayoffData[] = [];
    const strike = strikePrice[0];
    const callCost = callPremium[0];
    const putCost = putPremium[0];

    for (let price = 50; price <= 150; price += 2) {
      // Call option intrinsic value
      const callIntrinsic = Math.max(0, price - strike);
      const callProfit = callIntrinsic - callCost;

      // Put option intrinsic value
      const putIntrinsic = Math.max(0, strike - price);
      const putProfit = putIntrinsic - putCost;

      data.push({
        stockPrice: price,
        callValue: callIntrinsic,
        putValue: putIntrinsic,
        callProfit,
        putProfit
      });
    }

    return data;
  };

  const data = generatePayoffData();

  const formatTooltip = (value: number, name: string) => {
    if (name === 'callProfit' || name === 'putProfit') {
      return [`$${value.toFixed(2)}`, name === 'callProfit' ? 'Call P&L' : 'Put P&L'];
    }
    return [`$${value.toFixed(2)}`, name === 'callValue' ? 'Call Value' : 'Put Value'];
  };

  const getBreakeven = () => {
    if (selectedOption === 'call') {
      return strikePrice[0] + callPremium[0];
    } else {
      return strikePrice[0] - putPremium[0];
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Interactive Options Payoff Diagram</CardTitle>
        <CardDescription>
          Adjust the parameters below to see how option values and profits change with stock price
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Option Type Selection */}
        <div className="flex gap-2">
          <Button
            variant={selectedOption === 'call' ? 'default' : 'outline'}
            onClick={() => setSelectedOption('call')}
            className="flex items-center gap-2"
          >
            <TrendingUp className="h-4 w-4" />
            Call Option
          </Button>
          <Button
            variant={selectedOption === 'put' ? 'default' : 'outline'}
            onClick={() => setSelectedOption('put')}
            className="flex items-center gap-2"
          >
            <TrendingDown className="h-4 w-4" />
            Put Option
          </Button>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Strike Price: ${strikePrice[0]}</label>
            <Slider
              value={strikePrice}
              onValueChange={setStrikePrice}
              max={130}
              min={70}
              step={5}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {selectedOption === 'call' ? 'Call' : 'Put'} Premium: ${selectedOption === 'call' ? callPremium[0] : putPremium[0]}
            </label>
            <Slider
              value={selectedOption === 'call' ? callPremium : putPremium}
              onValueChange={selectedOption === 'call' ? setCallPremium : setPutPremium}
              max={15}
              min={1}
              step={0.5}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Breakeven Price</label>
            <Badge variant="outline" className="w-full justify-center py-2">
              ${getBreakeven().toFixed(2)}
            </Badge>
          </div>
        </div>

        {/* Chart */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="stockPrice" 
                type="number"
                scale="linear"
                domain={['dataMin', 'dataMax']}
                tickFormatter={(value) => `$${value}`}
              />
              <YAxis tickFormatter={(value) => `$${value}`} />
              <Tooltip 
                formatter={formatTooltip}
                labelFormatter={(value) => `Stock Price: $${value}`}
              />
              <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
              <ReferenceLine x={strikePrice[0]} stroke="#888" strokeDasharray="2 2" />
              <ReferenceLine x={getBreakeven()} stroke="#f59e0b" strokeDasharray="4 4" />
              
              {selectedOption === 'call' ? (
                <>
                  <Line 
                    type="monotone" 
                    dataKey="callValue" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    name="Intrinsic Value"
                    dot={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="callProfit" 
                    stroke="#3b82f6" 
                    strokeWidth={3}
                    name="Profit/Loss"
                    dot={false}
                  />
                </>
              ) : (
                <>
                  <Line 
                    type="monotone" 
                    dataKey="putValue" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    name="Intrinsic Value"
                    dot={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="putProfit" 
                    stroke="#8b5cf6" 
                    strokeWidth={3}
                    name="Profit/Loss"
                    dot={false}
                  />
                </>
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Key Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">
              {selectedOption === 'call' ? 'Call Option' : 'Put Option'} Insights
            </h4>
            <ul className="text-sm space-y-1">
              {selectedOption === 'call' ? (
                <>
                  <li>• Profits when stock price {'>'} strike + premium</li>
                  <li>• Maximum loss is the premium paid</li>
                  <li>• Unlimited profit potential above breakeven</li>
                  <li>• Time decay works against you</li>
                </>
              ) : (
                <>
                  <li>• Profits when stock price {'<'} strike - premium</li>
                  <li>• Maximum loss is the premium paid</li>
                  <li>• Maximum profit is strike - premium</li>
                  <li>• Time decay works against you</li>
                </>
              )}
            </ul>
          </div>

          <div className="p-3 bg-amber-50 dark:bg-amber-950 rounded-lg">
            <h4 className="font-medium text-amber-700 dark:text-amber-300 mb-2">Key Levels</h4>
            <div className="text-sm space-y-1">
              <div className="flex justify-between">
                <span>Strike Price:</span>
                <span className="font-medium">${strikePrice[0]}</span>
              </div>
              <div className="flex justify-between">
                <span>Premium Paid:</span>
                <span className="font-medium">${selectedOption === 'call' ? callPremium[0] : putPremium[0]}</span>
              </div>
              <div className="flex justify-between">
                <span>Breakeven:</span>
                <span className="font-medium">${getBreakeven().toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Max Loss:</span>
                <span className="font-medium text-red-600">
                  ${selectedOption === 'call' ? callPremium[0] : putPremium[0]}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
