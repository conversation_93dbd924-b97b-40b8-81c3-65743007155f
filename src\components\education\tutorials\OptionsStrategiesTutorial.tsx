
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ChevronLeft, 
  ChevronRight, 
  BookOpen, 
  Target, 
  Shield, 
  TrendingUp,
  TrendingDown,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { OptionsPayoffDiagram } from './visualizations/OptionsPayoffDiagram';
import { CoveredCallDemo } from './visualizations/CoveredCallDemo';
import { ProtectivePutDemo } from './visualizations/ProtectivePutDemo';
import { ComplexStrategiesDemo } from './visualizations/ComplexStrategiesDemo';

interface OptionsStrategiesTutorialProps {
  onComplete: () => void;
  onExit: () => void;
}

export const OptionsStrategiesTutorial = React.memo(function OptionsStrategiesTutorial({
  onComplete,
  onExit
}: OptionsStrategiesTutorialProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [quizAnswers, setQuizAnswers] = useState<Record<number, string>>({});
  const [showQuizResults, setShowQuizResults] = useState<Record<number, boolean>>({});

  const totalSteps = 8;

  const handleStepComplete = (step: number) => {
    setCompletedSteps(prev => new Set([...prev, step]));
  };

  const handleQuizAnswer = (questionId: number, answer: string) => {
    setQuizAnswers(prev => ({ ...prev, [questionId]: answer }));
    setShowQuizResults(prev => ({ ...prev, [questionId]: true }));
  };

  const isCorrectAnswer = (questionId: number, answer: string): boolean => {
    const correctAnswers: Record<number, string> = {
      1: 'b', // Limited profit, limited risk for covered calls
      2: 'c', // Protective put acts as insurance
      3: 'a', // Iron condor profits from low volatility
    };
    return correctAnswers[questionId] === answer;
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Target className="h-16 w-16 mx-auto mb-4 text-primary" />
              <h2 className="text-2xl font-bold mb-2">Welcome to Options Trading Strategies</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Master the art of options trading with comprehensive strategies including covered calls, 
                protective puts, and advanced multi-leg strategies. Learn to manage risk and enhance returns.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="text-center">
                  <Shield className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                  <CardTitle className="text-lg">Basic Strategies</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Learn covered calls and protective puts for risk management and income generation.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <BarChart3 className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <CardTitle className="text-lg">Advanced Strategies</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Master spreads, straddles, and iron condors for complex market scenarios.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-orange-500" />
                  <CardTitle className="text-lg">Risk Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Understand the risks and rewards of each strategy with payoff diagrams.
                  </p>
                </CardContent>
              </Card>
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> Options trading involves significant risk and requires thorough understanding. 
                This tutorial is for educational purposes only and not financial advice.
              </AlertDescription>
            </Alert>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Options Basics & Payoff Diagrams</h2>
              <p className="text-muted-foreground mb-6">
                Before diving into strategies, let's understand how options work and how to read payoff diagrams.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Call vs Put Options</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      <strong>Call Options:</strong>
                    </div>
                    <p className="text-sm text-muted-foreground ml-6">
                      Give you the right to BUY a stock at a specific price. Profit when stock price rises.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <TrendingDown className="h-4 w-4 text-red-500" />
                      <strong>Put Options:</strong>
                    </div>
                    <p className="text-sm text-muted-foreground ml-6">
                      Give you the right to SELL a stock at a specific price. Profit when stock price falls.
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <strong>Key Terms:</strong>
                    <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                      <li>• <strong>Strike Price:</strong> The price at which you can exercise</li>
                      <li>• <strong>Premium:</strong> The cost to buy the option</li>
                      <li>• <strong>Expiration:</strong> When the option expires</li>
                      <li>• <strong>Intrinsic Value:</strong> Current profit if exercised</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <div>
                <OptionsPayoffDiagram />
              </div>
            </div>

            <Button onClick={() => handleStepComplete(2)} className="w-full">
              Understanding Options Basics ✓
            </Button>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Covered Call Strategy</h2>
              <p className="text-muted-foreground mb-6">
                A covered call involves owning shares and selling call options against them. 
                It's a conservative strategy for generating income from existing stock positions.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-blue-500" />
                    How Covered Calls Work
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-1">1</Badge>
                      <div>
                        <p className="font-medium">Own 100 shares of stock</p>
                        <p className="text-sm text-muted-foreground">You must own the underlying shares</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-1">2</Badge>
                      <div>
                        <p className="font-medium">Sell a call option</p>
                        <p className="text-sm text-muted-foreground">Collect premium income immediately</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-1">3</Badge>
                      <div>
                        <p className="font-medium">Keep premium if option expires worthless</p>
                        <p className="text-sm text-muted-foreground">Or sell shares at strike price if called away</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium text-green-600">Pros:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Generate income from existing positions</li>
                      <li>• Reduced cost basis from premium collected</li>
                      <li>• Limited downside protection</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-red-600">Cons:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Limited upside potential</li>
                      <li>• Still exposed to downside risk</li>
                      <li>• May miss out on large gains</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <div>
                <CoveredCallDemo />
              </div>
            </div>

            <Button onClick={() => handleStepComplete(3)} className="w-full">
              Mastered Covered Calls ✓
            </Button>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Protective Put Strategy</h2>
              <p className="text-muted-foreground mb-6">
                A protective put acts like insurance for your stock position. You own shares and buy put options 
                to protect against downside risk.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-500" />
                    How Protective Puts Work
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-1">1</Badge>
                      <div>
                        <p className="font-medium">Own 100 shares of stock</p>
                        <p className="text-sm text-muted-foreground">Your underlying position</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-1">2</Badge>
                      <div>
                        <p className="font-medium">Buy a put option</p>
                        <p className="text-sm text-muted-foreground">Pay premium for downside protection</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-1">3</Badge>
                      <div>
                        <p className="font-medium">Exercise put if stock falls</p>
                        <p className="text-sm text-muted-foreground">Sell shares at strike price regardless of market price</p>
                      </div>
                    </div>
                  </div>

                  <Alert>
                    <Shield className="h-4 w-4" />
                    <AlertDescription>
                      Think of protective puts like car insurance - you pay a premium to protect against major losses.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-2">
                    <h4 className="font-medium text-green-600">Pros:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Unlimited upside potential</li>
                      <li>• Limited downside risk</li>
                      <li>• Peace of mind during volatility</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-red-600">Cons:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Premium cost reduces profits</li>
                      <li>• Time decay works against you</li>
                      <li>• May be expensive for volatile stocks</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <div>
                <ProtectivePutDemo />
              </div>
            </div>

            <Button onClick={() => handleStepComplete(4)} className="w-full">
              Understood Protective Puts ✓
            </Button>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Quiz: Basic Options Strategies</h2>
              <p className="text-muted-foreground mb-6">
                Test your understanding of covered calls and protective puts.
              </p>
            </div>

            <div className="space-y-6">
              {/* Question 1 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Question 1: Covered Call Risk Profile</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>What is the risk/reward profile of a covered call strategy?</p>
                  
                  <div className="space-y-2">
                    {[
                      { id: 'a', text: 'Unlimited profit, unlimited risk' },
                      { id: 'b', text: 'Limited profit, limited risk' },
                      { id: 'c', text: 'Limited profit, unlimited risk' },
                      { id: 'd', text: 'Unlimited profit, limited risk' }
                    ].map(option => (
                      <Button
                        key={option.id}
                        variant={quizAnswers[1] === option.id ? "default" : "outline"}
                        className="w-full justify-start text-left h-auto p-3"
                        onClick={() => handleQuizAnswer(1, option.id)}
                      >
                        <span className="font-medium mr-2">{option.id.toUpperCase()}.</span>
                        {option.text}
                      </Button>
                    ))}
                  </div>

                  {showQuizResults[1] && (
                    <Alert className={isCorrectAnswer(1, quizAnswers[1] || '') ? "border-green-500" : "border-red-500"}>
                      {isCorrectAnswer(1, quizAnswers[1] || '') ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <AlertDescription>
                        {isCorrectAnswer(1, quizAnswers[1] || '') 
                          ? "Correct! Covered calls have limited profit (strike price + premium) and limited risk (stock ownership risk minus premium collected)."
                          : "Incorrect. Covered calls limit your upside to the strike price plus premium, but you still own the stock which carries downside risk."
                        }
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Question 2 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Question 2: Protective Put Purpose</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>What is the primary purpose of a protective put strategy?</p>
                  
                  <div className="space-y-2">
                    {[
                      { id: 'a', text: 'Generate income from existing positions' },
                      { id: 'b', text: 'Speculate on price direction' },
                      { id: 'c', text: 'Protect against downside risk' },
                      { id: 'd', text: 'Amplify potential gains' }
                    ].map(option => (
                      <Button
                        key={option.id}
                        variant={quizAnswers[2] === option.id ? "default" : "outline"}
                        className="w-full justify-start text-left h-auto p-3"
                        onClick={() => handleQuizAnswer(2, option.id)}
                      >
                        <span className="font-medium mr-2">{option.id.toUpperCase()}.</span>
                        {option.text}
                      </Button>
                    ))}
                  </div>

                  {showQuizResults[2] && (
                    <Alert className={isCorrectAnswer(2, quizAnswers[2] || '') ? "border-green-500" : "border-red-500"}>
                      {isCorrectAnswer(2, quizAnswers[2] || '') ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                      <AlertDescription>
                        {isCorrectAnswer(2, quizAnswers[2] || '') 
                          ? "Correct! Protective puts act like insurance, protecting your stock position from significant downside moves."
                          : "Incorrect. The primary purpose of protective puts is downside protection, acting like insurance for your stock position."
                        }
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </div>

            <Button 
              onClick={() => handleStepComplete(5)} 
              className="w-full"
              disabled={!showQuizResults[1] || !showQuizResults[2]}
            >
              Continue to Advanced Strategies
            </Button>
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Complex Options Strategies</h2>
              <p className="text-muted-foreground mb-6">
                Learn advanced multi-leg strategies including spreads, straddles, and iron condors for different market scenarios.
              </p>
            </div>

            <ComplexStrategiesDemo />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Bull Call Spread</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Buy a call at lower strike, sell a call at higher strike. 
                    Bullish strategy with limited risk and reward.
                  </p>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Max Profit:</span>
                      <span className="text-green-600">Strike Difference - Net Premium</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Loss:</span>
                      <span className="text-red-600">Net Premium Paid</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Best Scenario:</span>
                      <span>Moderate upward movement</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Iron Condor</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Sell put spread + sell call spread. Profits from low volatility 
                    when stock stays within a range.
                  </p>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Max Profit:</span>
                      <span className="text-green-600">Net Premium Collected</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Loss:</span>
                      <span className="text-red-600">Strike Width - Net Premium</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Best Scenario:</span>
                      <span>Low volatility, sideways movement</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Long Straddle</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Buy call and put at same strike. Profits from high volatility 
                    regardless of direction.
                  </p>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Max Profit:</span>
                      <span className="text-green-600">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Loss:</span>
                      <span className="text-red-600">Total Premium Paid</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Best Scenario:</span>
                      <span>High volatility, big moves</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Butterfly Spread</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Buy 1 low strike, sell 2 middle strikes, buy 1 high strike. 
                    Profits if stock stays near middle strike.
                  </p>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Max Profit:</span>
                      <span className="text-green-600">Strike Difference - Net Premium</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Loss:</span>
                      <span className="text-red-600">Net Premium Paid</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Best Scenario:</span>
                      <span>Stock stays at middle strike</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Button onClick={() => handleStepComplete(6)} className="w-full">
              Understood Complex Strategies ✓
            </Button>
          </div>
        );

      case 7:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Strategy Selection & Risk Management</h2>
              <p className="text-muted-foreground mb-6">
                Learn when to use each strategy and how to manage risk in your options trading.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Market Outlook Strategy Guide</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <strong className="text-green-700 dark:text-green-300">Bullish Outlook</strong>
                      </div>
                      <ul className="text-sm space-y-1">
                        <li>• Long calls for aggressive upside</li>
                        <li>• Bull call spreads for moderate gains</li>
                        <li>• Sell puts for income + potential ownership</li>
                      </ul>
                    </div>

                    <div className="p-3 bg-red-50 dark:bg-red-950 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingDown className="h-4 w-4 text-red-600" />
                        <strong className="text-red-700 dark:text-red-300">Bearish Outlook</strong>
                      </div>
                      <ul className="text-sm space-y-1">
                        <li>• Long puts for aggressive downside</li>
                        <li>• Bear put spreads for moderate gains</li>
                        <li>• Protective puts for portfolio protection</li>
                      </ul>
                    </div>

                    <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <BarChart3 className="h-4 w-4 text-blue-600" />
                        <strong className="text-blue-700 dark:text-blue-300">Neutral Outlook</strong>
                      </div>
                      <ul className="text-sm space-y-1">
                        <li>• Iron condors for range-bound markets</li>
                        <li>• Covered calls for income generation</li>
                        <li>• Butterfly spreads for minimal movement</li>
                      </ul>
                    </div>

                    <div className="p-3 bg-orange-50 dark:bg-orange-950 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-orange-600" />
                        <strong className="text-orange-700 dark:text-orange-300">High Volatility Expected</strong>
                      </div>
                      <ul className="text-sm space-y-1">
                        <li>• Long straddles for big moves</li>
                        <li>• Long strangles for directional uncertainty</li>
                        <li>• Avoid selling premium strategies</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Risk Management Rules</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Position Sizing</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Never risk more than 2-5% per trade</li>
                        <li>• Size positions based on probability of success</li>
                        <li>• Consider maximum loss scenarios</li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Exit Strategies</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Set profit targets (often 25-50% of max profit)</li>
                        <li>• Use stop losses (typically 2x premium collected)</li>
                        <li>• Close positions before expiration if needed</li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Time Decay Management</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Be aware of theta decay acceleration</li>
                        <li>• Consider rolling positions when appropriate</li>
                        <li>• Avoid holding long options into final week</li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Volatility Considerations</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• Buy options when IV is low</li>
                        <li>• Sell options when IV is high</li>
                        <li>• Monitor earnings and events</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Remember:</strong> Start with paper trading to practice these strategies. 
                Options can expire worthless, so never invest more than you can afford to lose.
              </AlertDescription>
            </Alert>

            <Button onClick={() => handleStepComplete(7)} className="w-full">
              Mastered Risk Management ✓
            </Button>
          </div>
        );

      case 8:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-4">Final Quiz: Advanced Options</h2>
              <p className="text-muted-foreground mb-6">
                Test your knowledge of complex strategies and risk management.
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Question 3: Iron Condor Strategy</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>When is an iron condor strategy most profitable?</p>
                
                <div className="space-y-2">
                  {[
                    { id: 'a', text: 'When volatility is low and stock stays within the strike range' },
                    { id: 'b', text: 'When the stock moves significantly in either direction' },
                    { id: 'c', text: 'When volatility increases dramatically' },
                    { id: 'd', text: 'When the stock gaps up or down at expiration' }
                  ].map(option => (
                    <Button
                      key={option.id}
                      variant={quizAnswers[3] === option.id ? "default" : "outline"}
                      className="w-full justify-start text-left h-auto p-3"
                      onClick={() => handleQuizAnswer(3, option.id)}
                    >
                      <span className="font-medium mr-2">{option.id.toUpperCase()}.</span>
                      {option.text}
                    </Button>
                  ))}
                </div>

                {showQuizResults[3] && (
                  <Alert className={isCorrectAnswer(3, quizAnswers[3] || '') ? "border-green-500" : "border-red-500"}>
                    {isCorrectAnswer(3, quizAnswers[3] || '') ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <AlertDescription>
                      {isCorrectAnswer(3, quizAnswers[3] || '') 
                        ? "Correct! Iron condors profit when the stock stays within the inner strike range and volatility remains low."
                        : "Incorrect. Iron condors are neutral strategies that profit from low volatility and minimal stock movement."
                      }
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>🎉 Congratulations!</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>You've completed the Options Trading Strategies tutorial! You now understand:</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Options basics and payoff diagrams
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Covered calls for income generation
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Protective puts for risk management
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Complex multi-leg strategies
                    </li>
                  </ul>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Strategy selection based on market outlook
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Risk management principles
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Position sizing and exit strategies
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      Volatility considerations
                    </li>
                  </ul>
                </div>

                <Alert>
                  <BookOpen className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Next Steps:</strong> Practice with paper trading, start with basic strategies, 
                    and gradually work up to more complex positions as you gain experience.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            <Button 
              onClick={() => handleStepComplete(8)} 
              className="w-full"
              disabled={!showQuizResults[3]}
            >
              Complete Tutorial ✓
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={onExit}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            Exit Tutorial
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Options Trading Strategies</h1>
            <p className="text-muted-foreground">Step {currentStep} of {totalSteps}</p>
          </div>
        </div>
        <Badge variant="outline">Advanced</Badge>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
        </div>
        <Progress value={(currentStep / totalSteps) * 100} className="h-2" />
      </div>

      {/* Content */}
      <div className="min-h-[600px]">
        {renderStep()}
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between pt-6 border-t">
        <Button 
          variant="outline" 
          onClick={prevStep}
          disabled={currentStep === 1}
          className="gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
        
        <div className="flex gap-1">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i + 1}
              className={`w-2 h-2 rounded-full ${
                i + 1 <= currentStep 
                  ? 'bg-primary' 
                  : 'bg-muted'
              }`}
            />
          ))}
        </div>

        <Button 
          onClick={nextStep}
          disabled={currentStep === totalSteps && !completedSteps.has(totalSteps)}
          className="gap-2"
        >
          {currentStep === totalSteps ? 'Complete' : 'Next'}
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
});
