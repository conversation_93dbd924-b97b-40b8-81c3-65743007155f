
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '@/components/layout/PageLayout';
import { WatchlistCard } from '@/components/watchlist/WatchlistCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { StockCard } from '@/components/stocks/StockCard';
import { useStockData, mockStocks } from '@/utils/stockApi';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { X, Plus, Search } from 'lucide-react';

const Watchlist = () => {
  const navigate = useNavigate();
  const stocks = useStockData();
  
  // Create multiple watchlists for demo purposes
  const [watchlists, setWatchlists] = useState<{id: string; name: string; stocks: typeof stocks}[]>([
    { 
      id: '1', 
      name: 'My Favorites', 
      stocks: stocks.filter(stock => ['AAPL', 'MSFT', 'NVDA'].includes(stock.symbol))
    },
    { 
      id: '2', 
      name: 'Tech Stocks', 
      stocks: stocks.filter(stock => ['AAPL', 'MSFT', 'GOOGL', 'META'].includes(stock.symbol))
    }
  ]);
  
  // State for modal
  const [isAddStockOpen, setIsAddStockOpen] = useState(false);
  const [isCreateListOpen, setIsCreateListOpen] = useState(false);
  const [selectedWatchlistId, setSelectedWatchlistId] = useState('1');
  const [newWatchlistName, setNewWatchlistName] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Handle search for stocks
  const filteredStocks = stocks.filter(stock => 
    stock.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    stock.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Handle adding stock to watchlist
  const handleAddToWatchlist = (watchlistId: string) => {
    setSelectedWatchlistId(watchlistId);
    setIsAddStockOpen(true);
  };
  
  // Handle adding a selected stock to the watchlist
  const handleAddStock = (stockSymbol: string) => {
    const stockToAdd = stocks.find(stock => stock.symbol === stockSymbol);
    if (!stockToAdd) return;
    
    setWatchlists(prev => prev.map(watchlist => {
      if (watchlist.id === selectedWatchlistId) {
        // Check if stock is already in the watchlist
        const isStockInWatchlist = watchlist.stocks.some(s => s.symbol === stockSymbol);
        if (isStockInWatchlist) return watchlist;
        
        return {
          ...watchlist,
          stocks: [...watchlist.stocks, stockToAdd]
        };
      }
      return watchlist;
    }));
    
    setIsAddStockOpen(false);
    setSearchTerm('');
  };
  
  // Handle removing stock from watchlist
  const handleRemoveFromWatchlist = (watchlistId: string, symbol: string) => {
    setWatchlists(prev => prev.map(watchlist => {
      if (watchlist.id === watchlistId) {
        return {
          ...watchlist,
          stocks: watchlist.stocks.filter(stock => stock.symbol !== symbol)
        };
      }
      return watchlist;
    }));
  };
  
  // Handle selecting a stock from watchlist
  const handleSelectWatchlistStock = (symbol: string) => {
    navigate(`/stocks?symbol=${symbol}`);
  };
  
  // Handle creating a new watchlist
  const handleCreateWatchlist = () => {
    if (!newWatchlistName.trim()) return;
    
    const newWatchlist = {
      id: Date.now().toString(),
      name: newWatchlistName,
      stocks: []
    };
    
    setWatchlists(prev => [...prev, newWatchlist]);
    setNewWatchlistName('');
    setIsCreateListOpen(false);
  };
  
  // Handle deleting a watchlist
  const handleDeleteWatchlist = (id: string) => {
    setWatchlists(prev => prev.filter(watchlist => watchlist.id !== id));
  };
  
  return (
    <PageLayout title="Watchlists">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Your Stock Watchlists</h2>
        <Button onClick={() => setIsCreateListOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create New List
        </Button>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        {watchlists.map((watchlist) => (
          <div key={watchlist.id} className="relative">
            <WatchlistCard 
              title={watchlist.name}
              stocks={watchlist.stocks}
              onAddClick={() => handleAddToWatchlist(watchlist.id)}
              onRemoveStock={(symbol) => handleRemoveFromWatchlist(watchlist.id, symbol)}
              onSelectStock={handleSelectWatchlistStock}
            />
            {watchlists.length > 1 && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 h-7 w-7 text-muted-foreground hover:text-foreground"
                onClick={() => handleDeleteWatchlist(watchlist.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        ))}
      </div>
      
      {/* Dialog for adding stocks to watchlist */}
      <Dialog open={isAddStockOpen} onOpenChange={setIsAddStockOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Stock to Watchlist</DialogTitle>
            <DialogDescription>
              Search and select a stock to add to your watchlist.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="relative mb-4">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search stocks by name or symbol..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <div className="max-h-[300px] overflow-y-auto space-y-2">
              {searchTerm.trim() === '' ? (
                <div className="text-center text-muted-foreground py-4">
                  Enter a search term to find stocks
                </div>
              ) : filteredStocks.length === 0 ? (
                <div className="text-center text-muted-foreground py-4">
                  No stocks found for "{searchTerm}"
                </div>
              ) : (
                filteredStocks.map(stock => (
                  <div 
                    key={stock.symbol}
                    className="p-3 flex items-center justify-between hover:bg-muted/30 transition-colors rounded-md cursor-pointer"
                    onClick={() => handleAddStock(stock.symbol)}
                  >
                    <div>
                      <p className="font-medium">{stock.symbol}</p>
                      <p className="text-xs text-muted-foreground">{stock.name}</p>
                    </div>
                    <Button size="sm" variant="secondary">
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                ))
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddStockOpen(false)}>Cancel</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Dialog for creating a new watchlist */}
      <Dialog open={isCreateListOpen} onOpenChange={setIsCreateListOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Create New Watchlist</DialogTitle>
            <DialogDescription>
              Enter a name for your new watchlist.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Label htmlFor="list-name">Watchlist Name</Label>
            <Input
              id="list-name"
              placeholder="My New Watchlist"
              value={newWatchlistName}
              onChange={(e) => setNewWatchlistName(e.target.value)}
              className="mt-2"
            />
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateListOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateWatchlist}>Create Watchlist</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </PageLayout>
  );
};

export default Watchlist;
