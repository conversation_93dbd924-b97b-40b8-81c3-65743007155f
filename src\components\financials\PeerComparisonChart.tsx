
import { 
  Bar<PERSON>hart, Bar, <PERSON>Axis, <PERSON>Axis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface PeerComparisonChartProps {
  data: {
    name: string;
    [key: string]: any;
  }[];
  stockSymbol: string;
}

export const PeerComparisonChart = ({ data, stockSymbol }: PeerComparisonChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Peer Comparison</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} layout="vertical">
            <CartesianGrid strokeDasharray="3 3" horizontal={false} />
            <XAxis type="number" />
            <YAxis dataKey="name" type="category" width={100} />
            <Tooltip />
            <Legend />
            <Bar 
              dataKey={stockSymbol} 
              fill="hsl(var(--primary))" 
              name={stockSymbol}
            />
            <Bar 
              dataKey="Peer Avg" 
              fill="hsl(var(--muted))" 
              name="Peer Average"
            />
            <Bar 
              dataKey="Industry Avg" 
              fill="hsl(var(--accent))" 
              name="Industry Average"
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
