
import { useState } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { ProspectiveFilters } from '@/components/prospective/ProspectiveFilters';
import { ProspectiveStockTable } from '@/components/prospective/ProspectiveStockTable';
import { ProspectiveStockDetail } from '@/components/prospective/ProspectiveStockDetail';
import { FilterOptions, ProspectiveStock } from '@/types/prospectiveStocks';
import { prospectiveStocks } from '@/utils/prospectiveStocksData';

const ProspectiveStocks = () => {
  const [selectedStock, setSelectedStock] = useState<ProspectiveStock | null>(null);
  const [filteredStocks, setFilteredStocks] = useState<ProspectiveStock[]>(
    prospectiveStocks.slice(0, 50)
  );
  
  const handleFilterChange = (filters: FilterOptions) => {
    let filtered = prospectiveStocks;
    
    // Filter by region
    if (filters.regions && filters.regions.length > 0) {
      filtered = filtered.filter(stock => filters.regions.includes(stock.region as any));
    }
    
    // Filter by sector
    if (filters.sectors && filters.sectors.length > 0) {
      filtered = filtered.filter(stock => filters.sectors.includes(stock.sector));
    }
    
    // Filter by market cap
    if (filters.minMarketCap) {
      filtered = filtered.filter(stock => stock.marketCap >= filters.minMarketCap!);
    }
    
    // Filter by PE ratio
    if (filters.maxPE) {
      filtered = filtered.filter(stock => stock.pe <= filters.maxPE!);
    }
    
    // Filter by dividend yield
    if (filters.minDividend) {
      filtered = filtered.filter(
        stock => (stock.dividend / stock.price * 100) >= filters.minDividend!
      );
    }
    
    // Filter by overall score
    if (filters.minOverallScore) {
      filtered = filtered.filter(stock => stock.overallScore >= filters.minOverallScore!);
    }
    
    // Get top 50 stocks by overall score
    setFilteredStocks(filtered.slice(0, 50));
    setSelectedStock(null);
  };
  
  const handleStockSelect = (stock: ProspectiveStock) => {
    setSelectedStock(stock);
  };

  return (
    <PageLayout title="Top 50 Prospective Stocks">
      <div className="space-y-6">
        <ProspectiveFilters onFilterChange={handleFilterChange} />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ProspectiveStockTable 
              stocks={filteredStocks} 
              onStockSelect={handleStockSelect} 
            />
          </div>
          
          <div className="lg:col-span-1">
            <ProspectiveStockDetail stock={selectedStock} />
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default ProspectiveStocks;
