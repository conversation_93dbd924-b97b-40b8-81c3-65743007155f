
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  ArrowRight, 
  X, 
  BarChart3, 
  TrendingUp, 
  Volume2,
  Eye,
  CheckCircle
} from 'lucide-react';
import { CandlestickPatternsChart } from './visualizations/CandlestickPatternsChart';
import { VolumeAnalysisChart } from './visualizations/VolumeAnalysisChart';
import { InteractiveChartReading } from './visualizations/InteractiveChartReading';
import { TechnicalIndicatorsDemo } from './visualizations/TechnicalIndicatorsDemo';

interface StockChartsReadingTutorialProps {
  onComplete: () => void;
  onExit: () => void;
}

export const StockChartsReadingTutorial = React.memo(function StockChartsReadingTutorial({
  onComplete,
  onExit
}: StockChartsReadingTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [quizAnswers, setQuizAnswers] = useState<{ [key: number]: string }>({});

  const steps = [
    {
      id: 0,
      title: "Introduction to Stock Charts",
      icon: <BarChart3 className="h-5 w-5" />,
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Welcome to Chart Reading Mastery</h3>
            <p className="text-lg text-muted-foreground mb-6">
              Learn to read and interpret stock charts like a professional trader
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4">
              <div className="flex items-center gap-3 mb-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                <h4 className="font-semibold">Chart Types</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Understand different chart types: line, bar, and candlestick charts
              </p>
            </Card>
            
            <Card className="p-4">
              <div className="flex items-center gap-3 mb-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <h4 className="font-semibold">Price Patterns</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Identify bullish and bearish candlestick patterns
              </p>
            </Card>
            
            <Card className="p-4">
              <div className="flex items-center gap-3 mb-2">
                <Volume2 className="h-5 w-5 text-purple-600" />
                <h4 className="font-semibold">Volume Analysis</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Learn how volume confirms price movements
              </p>
            </Card>
            
            <Card className="p-4">
              <div className="flex items-center gap-3 mb-2">
                <Eye className="h-5 w-5 text-orange-600" />
                <h4 className="font-semibold">Technical Indicators</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                Master moving averages, RSI, and MACD
              </p>
            </Card>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">What You'll Learn:</h4>
            <ul className="text-sm space-y-1">
              <li>• How to read candlestick charts</li>
              <li>• Common bullish and bearish patterns</li>
              <li>• Volume analysis techniques</li>
              <li>• Basic technical indicators</li>
              <li>• Chart timeframes and their uses</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 1,
      title: "Understanding Candlestick Charts",
      icon: <BarChart3 className="h-5 w-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-bold mb-4">Candlestick Chart Basics</h3>
            <p className="text-muted-foreground mb-6">
              Candlestick charts show four key price points: open, high, low, and close (OHLC)
            </p>
          </div>

          <CandlestickPatternsChart />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4">
              <h4 className="font-semibold mb-2 text-green-600">Bullish Candle</h4>
              <p className="text-sm text-muted-foreground">
                Green/white candle where closing price is higher than opening price. 
                Indicates buying pressure and upward momentum.
              </p>
            </Card>
            
            <Card className="p-4">
              <h4 className="font-semibold mb-2 text-red-600">Bearish Candle</h4>
              <p className="text-sm text-muted-foreground">
                Red/black candle where closing price is lower than opening price. 
                Indicates selling pressure and downward momentum.
              </p>
            </Card>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Key Components:</h4>
            <ul className="text-sm space-y-1">
              <li><strong>Body:</strong> The thick part showing open and close prices</li>
              <li><strong>Wicks/Shadows:</strong> Thin lines showing high and low prices</li>
              <li><strong>Color:</strong> Green for bullish, red for bearish</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 2,
      title: "Volume Analysis",
      icon: <Volume2 className="h-5 w-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-bold mb-4">Volume: The Market's Fuel</h3>
            <p className="text-muted-foreground mb-6">
              Volume shows the number of shares traded and confirms the strength of price movements
            </p>
          </div>

          <VolumeAnalysisChart />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4 border-green-200">
              <h4 className="font-semibold mb-2 text-green-600">High Volume + Price Up</h4>
              <p className="text-sm text-muted-foreground">
                Strong bullish signal. Many buyers are driving the price higher.
                This confirms the upward trend.
              </p>
            </Card>
            
            <Card className="p-4 border-red-200">
              <h4 className="font-semibold mb-2 text-red-600">High Volume + Price Down</h4>
              <p className="text-sm text-muted-foreground">
                Strong bearish signal. Heavy selling pressure is pushing prices lower.
                This confirms the downward trend.
              </p>
            </Card>

            <Card className="p-4 border-yellow-200">
              <h4 className="font-semibold mb-2 text-yellow-600">Low Volume + Price Change</h4>
              <p className="text-sm text-muted-foreground">
                Weak signal. Price movement without strong volume may not be sustainable.
                Be cautious of these moves.
              </p>
            </Card>

            <Card className="p-4 border-blue-200">
              <h4 className="font-semibold mb-2 text-blue-600">Volume Spikes</h4>
              <p className="text-sm text-muted-foreground">
                Sudden volume increases often signal important news or institutional activity.
                Watch for breakouts or breakdowns.
              </p>
            </Card>
          </div>
        </div>
      )
    },
    {
      id: 3,
      title: "Technical Indicators",
      icon: <TrendingUp className="h-5 w-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-bold mb-4">Essential Technical Indicators</h3>
            <p className="text-muted-foreground mb-6">
              Learn the most important indicators that help identify trends and entry/exit points
            </p>
          </div>

          <TechnicalIndicatorsDemo />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-4">
              <h4 className="font-semibold mb-2">Moving Averages</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Smooth out price data to identify trend direction
              </p>
              <ul className="text-xs space-y-1">
                <li>• SMA: Simple Moving Average</li>
                <li>• EMA: Exponential Moving Average</li>
                <li>• Golden Cross: 50 MA crosses above 200 MA</li>
              </ul>
            </Card>
            
            <Card className="p-4">
              <h4 className="font-semibold mb-2">RSI (0-100)</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Measures if a stock is overbought or oversold
              </p>
              <ul className="text-xs space-y-1">
                <li>• Above 70: Potentially overbought</li>
                <li>• Below 30: Potentially oversold</li>
                <li>• 50: Neutral momentum</li>
              </ul>
            </Card>

            <Card className="p-4">
              <h4 className="font-semibold mb-2">MACD</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Shows relationship between two moving averages
              </p>
              <ul className="text-xs space-y-1">
                <li>• MACD Line above Signal: Bullish</li>
                <li>• MACD Line below Signal: Bearish</li>
                <li>• Histogram shows momentum</li>
              </ul>
            </Card>
          </div>
        </div>
      )
    },
    {
      id: 4,
      title: "Interactive Chart Reading",
      icon: <Eye className="h-5 w-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-bold mb-4">Practice Chart Reading</h3>
            <p className="text-muted-foreground mb-6">
              Test your skills with this interactive chart reading exercise
            </p>
          </div>

          <InteractiveChartReading />

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Chart Reading Tips:</h4>
            <ul className="text-sm space-y-1">
              <li>• Always start with the overall trend</li>
              <li>• Look for support and resistance levels</li>
              <li>• Confirm patterns with volume</li>
              <li>• Use multiple timeframes for context</li>
              <li>• Practice identifying key patterns daily</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 5,
      title: "Knowledge Check",
      icon: <CheckCircle className="h-5 w-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-bold mb-4">Test Your Knowledge</h3>
            <p className="text-muted-foreground mb-6">
              Answer these questions to verify your understanding
            </p>
          </div>

          <div className="space-y-6">
            {/* Quiz Question 1 */}
            <Card className="p-4">
              <h4 className="font-semibold mb-3">1. What does a green candlestick indicate?</h4>
              <div className="space-y-2">
                {['Price closed higher than it opened', 'Price closed lower than it opened', 'No price movement', 'High volume'].map((option, index) => (
                  <label key={index} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="q1"
                      value={option}
                      onChange={(e) => setQuizAnswers({...quizAnswers, 1: e.target.value})}
                      className="text-primary"
                    />
                    <span className="text-sm">{option}</span>
                  </label>
                ))}
              </div>
            </Card>

            {/* Quiz Question 2 */}
            <Card className="p-4">
              <h4 className="font-semibold mb-3">2. High volume with price increase suggests:</h4>
              <div className="space-y-2">
                {['Strong buying pressure', 'Weak trend', 'Market manipulation', 'Random movement'].map((option, index) => (
                  <label key={index} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="q2"
                      value={option}
                      onChange={(e) => setQuizAnswers({...quizAnswers, 2: e.target.value})}
                      className="text-primary"
                    />
                    <span className="text-sm">{option}</span>
                  </label>
                ))}
              </div>
            </Card>

            {/* Quiz Question 3 */}
            <Card className="p-4">
              <h4 className="font-semibold mb-3">3. An RSI reading above 70 typically indicates:</h4>
              <div className="space-y-2">
                {['Oversold condition', 'Overbought condition', 'Neutral momentum', 'Strong trend'].map((option, index) => (
                  <label key={index} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="q3"
                      value={option}
                      onChange={(e) => setQuizAnswers({...quizAnswers, 3: e.target.value})}
                      className="text-primary"
                    />
                    <span className="text-sm">{option}</span>
                  </label>
                ))}
              </div>
            </Card>
          </div>

          {Object.keys(quizAnswers).length === 3 && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Great job!</h4>
              <p className="text-sm text-green-700">
                You've completed all the questions. Review your answers and proceed when ready.
              </p>
            </div>
          )}
        </div>
      )
    }
  ];

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Reading Stock Charts</h1>
          <p className="text-muted-foreground">Understand candlestick patterns, volume, and basic chart analysis</p>
        </div>
        <Button variant="ghost" size="sm" onClick={onExit}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{currentStep + 1} of {steps.length}</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Step Indicator */}
      <div className="flex items-center gap-4 overflow-x-auto pb-4">
        {steps.map((step, index) => (
          <div 
            key={step.id}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg whitespace-nowrap ${
              index === currentStep 
                ? 'bg-primary text-primary-foreground' 
                : index < currentStep 
                  ? 'bg-green-100 text-green-800'
                  : 'bg-muted text-muted-foreground'
            }`}
          >
            {step.icon}
            <span className="text-sm font-medium">{step.title}</span>
            {index < currentStep && <CheckCircle className="h-4 w-4" />}
          </div>
        ))}
      </div>

      {/* Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {currentStepData.icon}
            {currentStepData.title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentStepData.content}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={onExit}>
            Exit Tutorial
          </Button>
          <Button onClick={handleNext}>
            {currentStep === steps.length - 1 ? 'Complete Tutorial' : 'Next'}
            {currentStep < steps.length - 1 && <ArrowRight className="h-4 w-4 ml-2" />}
          </Button>
        </div>
      </div>
    </div>
  );
});
