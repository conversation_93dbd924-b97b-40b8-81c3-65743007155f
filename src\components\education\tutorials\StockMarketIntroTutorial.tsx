
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowRight, ArrowLeft, BookOpen, TrendingUp, TrendingDown, 
  DollarSign, Users, Building2, PieChart, BarChart3, CheckCircle 
} from 'lucide-react';
import { MarketVisualization } from './visualizations/MarketVisualization';
import { TradingFlowDiagram } from './visualizations/TradingFlowDiagram';
import { MarketParticipantsChart } from './visualizations/MarketParticipantsChart';
import { cn } from '@/lib/utils';

interface TutorialStep {
  id: string;
  title: string;
  content: React.ReactNode;
  keyTerms: string[];
  quiz?: {
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  };
}

interface StockMarketIntroTutorialProps {
  onComplete: () => void;
  onExit: () => void;
  className?: string;
}

export const StockMarketIntroTutorial = React.memo(function StockMarketIntroTutorial({
  onComplete,
  onExit,
  className
}: StockMarketIntroTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [quizAnswers, setQuizAnswers] = useState<Record<number, number>>({});

  const steps: TutorialStep[] = [
    {
      id: 'overview',
      title: 'What is a Stock Market?',
      keyTerms: ['Stock Market', 'Securities', 'Exchange', 'Trading'],
      content: (
        <div className="space-y-6">
          <div className="prose max-w-none">
            <p className="text-lg leading-relaxed">
              A stock market is a platform where investors can buy and sell shares of publicly traded companies. 
              Think of it as a marketplace, but instead of buying groceries or clothes, you're buying ownership 
              stakes in businesses.
            </p>
          </div>
          
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <Building2 className="h-5 w-5" />
                Real-World Example
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-700">
                When you buy a share of Apple stock, you become a tiny owner of Apple Inc. 
                If Apple does well, your share value increases. If it struggles, the value may decrease.
              </p>
            </CardContent>
          </Card>

          <MarketVisualization />
        </div>
      ),
      quiz: {
        question: "What happens when you buy a stock?",
        options: [
          "You lend money to the company",
          "You become a partial owner of the company",
          "You get a guaranteed return",
          "You work for the company"
        ],
        correctAnswer: 1,
        explanation: "When you buy stock, you purchase ownership shares in the company, making you a shareholder with certain rights."
      }
    },
    {
      id: 'participants',
      title: 'Who Participates in Stock Markets?',
      keyTerms: ['Investors', 'Traders', 'Institutions', 'Retail Investors'],
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-500" />
                  Retail Investors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Individual investors like you and me who buy stocks for personal investment goals.</p>
                <ul className="mt-2 space-y-1 text-sm text-muted-foreground">
                  <li>• Long-term wealth building</li>
                  <li>• Retirement planning</li>
                  <li>• Personal financial goals</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-green-500" />
                  Institutional Investors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Large organizations that invest on behalf of others or themselves.</p>
                <ul className="mt-2 space-y-1 text-sm text-muted-foreground">
                  <li>• Mutual funds</li>
                  <li>• Pension funds</li>
                  <li>• Insurance companies</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <MarketParticipantsChart />
        </div>
      ),
      quiz: {
        question: "Which type of investor typically has more influence on stock prices?",
        options: [
          "Retail investors",
          "Institutional investors",
          "Both have equal influence",
          "Neither affects prices"
        ],
        correctAnswer: 1,
        explanation: "Institutional investors typically have more influence due to their large transaction volumes and significant capital."
      }
    },
    {
      id: 'how-trading-works',
      title: 'How Stock Trading Works',
      keyTerms: ['Bid', 'Ask', 'Spread', 'Order Types', 'Market Orders'],
      content: (
        <div className="space-y-6">
          <div className="prose max-w-none">
            <p>
              Stock trading involves matching buyers and sellers. Every stock has two key prices:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-green-800 flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Bid Price
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-green-700">
                  The highest price a buyer is willing to pay for a stock.
                </p>
              </CardContent>
            </Card>

            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="text-red-800 flex items-center gap-2">
                  <TrendingDown className="h-5 w-5" />
                  Ask Price
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-red-700">
                  The lowest price a seller is willing to accept for a stock.
                </p>
              </CardContent>
            </Card>
          </div>

          <TradingFlowDiagram />

          <Card className="bg-amber-50 border-amber-200">
            <CardHeader>
              <CardTitle className="text-amber-800">The Spread</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-amber-700">
                The difference between the bid and ask price is called the "spread." 
                A smaller spread typically indicates a more liquid (easily traded) stock.
              </p>
            </CardContent>
          </Card>
        </div>
      ),
      quiz: {
        question: "If a stock has a bid of $100 and an ask of $101, what is the spread?",
        options: ["$100", "$101", "$1", "$201"],
        correctAnswer: 2,
        explanation: "The spread is the difference between the ask price ($101) and bid price ($100), which equals $1."
      }
    },
    {
      id: 'key-concepts',
      title: 'Essential Stock Market Concepts',
      keyTerms: ['Market Cap', 'P/E Ratio', 'Dividends', 'Volatility'],
      content: (
        <div className="space-y-6">
          <Tabs defaultValue="market-cap" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="market-cap">Market Cap</TabsTrigger>
              <TabsTrigger value="pe-ratio">P/E Ratio</TabsTrigger>
              <TabsTrigger value="dividends">Dividends</TabsTrigger>
              <TabsTrigger value="volatility">Volatility</TabsTrigger>
            </TabsList>

            <TabsContent value="market-cap" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5" />
                    Market Capitalization
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="mb-4">
                    Market cap is the total value of a company's shares. It's calculated by multiplying 
                    the stock price by the number of outstanding shares.
                  </p>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <p className="font-mono text-center">
                      Market Cap = Stock Price × Number of Outstanding Shares
                    </p>
                  </div>
                  <div className="mt-4 grid grid-cols-3 gap-2 text-sm">
                    <div className="text-center">
                      <Badge variant="outline" className="bg-blue-50">Large Cap</Badge>
                      <p className="mt-1 text-muted-foreground">$10B+</p>
                    </div>
                    <div className="text-center">
                      <Badge variant="outline" className="bg-yellow-50">Mid Cap</Badge>
                      <p className="mt-1 text-muted-foreground">$2B-$10B</p>
                    </div>
                    <div className="text-center">
                      <Badge variant="outline" className="bg-green-50">Small Cap</Badge>
                      <p className="mt-1 text-muted-foreground">$300M-$2B</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="pe-ratio" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Price-to-Earnings (P/E) Ratio
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="mb-4">
                    The P/E ratio helps determine if a stock is overvalued or undervalued by comparing 
                    its price to its earnings per share.
                  </p>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <p className="font-mono text-center">
                      P/E Ratio = Stock Price ÷ Earnings Per Share
                    </p>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-muted-foreground">
                      A high P/E might indicate growth expectations, while a low P/E might suggest 
                      the stock is undervalued or facing challenges.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="dividends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Dividends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="mb-4">
                    Dividends are cash payments some companies make to shareholders, 
                    typically on a quarterly basis.
                  </p>
                  <div className="space-y-2">
                    <div className="flex justify-between p-2 bg-green-50 rounded">
                      <span>Dividend Yield</span>
                      <span className="font-mono">Annual Dividends ÷ Stock Price</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Not all companies pay dividends. Growth companies often reinvest profits 
                      instead of paying dividends.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="volatility" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Volatility</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="mb-4">
                    Volatility measures how much a stock's price fluctuates. Higher volatility 
                    means larger price swings.
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-center p-3 bg-green-50 rounded">
                      <div className="font-semibold text-green-800">Low Volatility</div>
                      <div className="text-green-600">Stable, predictable</div>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded">
                      <div className="font-semibold text-red-800">High Volatility</div>
                      <div className="text-red-600">Unpredictable, risky</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      ),
      quiz: {
        question: "A company with 1 million shares trading at $50 per share has a market cap of:",
        options: ["$1 million", "$50 million", "$500 million", "$5 billion"],
        correctAnswer: 1,
        explanation: "Market cap = 1 million shares × $50 = $50 million"
      }
    }
  ];

  const handleNextStep = () => {
    setCompletedSteps(prev => new Set([...prev, currentStep]));
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleQuizAnswer = (stepIndex: number, answerIndex: number) => {
    setQuizAnswers(prev => ({ ...prev, [stepIndex]: answerIndex }));
  };

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;
  const isLastStep = currentStep === steps.length - 1;

  return (
    <div className={cn("max-w-4xl mx-auto space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-6 w-6" />
                Introduction to Stock Markets
              </CardTitle>
              <CardDescription>
                Step {currentStep + 1} of {steps.length}: {currentStepData.title}
              </CardDescription>
            </div>
            <Button variant="outline" onClick={onExit}>
              Exit Tutorial
            </Button>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardHeader>
      </Card>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>{currentStepData.title}</CardTitle>
          <div className="flex flex-wrap gap-2">
            {currentStepData.keyTerms.map(term => (
              <Badge key={term} variant="secondary">
                {term}
              </Badge>
            ))}
          </div>
        </CardHeader>
        <CardContent>
          {currentStepData.content}
        </CardContent>
      </Card>

      {/* Quiz Section */}
      {currentStepData.quiz && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Quick Check
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="font-medium">{currentStepData.quiz.question}</p>
            <div className="space-y-2">
              {currentStepData.quiz.options.map((option, index) => (
                <Button
                  key={index}
                  variant={quizAnswers[currentStep] === index ? "default" : "outline"}
                  className="w-full justify-start"
                  onClick={() => handleQuizAnswer(currentStep, index)}
                >
                  {option}
                </Button>
              ))}
            </div>
            {quizAnswers[currentStep] !== undefined && (
              <Card className={quizAnswers[currentStep] === currentStepData.quiz.correctAnswer ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
                <CardContent className="pt-4">
                  <p className={quizAnswers[currentStep] === currentStepData.quiz.correctAnswer ? "text-green-800" : "text-red-800"}>
                    {quizAnswers[currentStep] === currentStepData.quiz.correctAnswer ? "Correct!" : "Not quite right."}
                  </p>
                  <p className="text-sm mt-2">{currentStepData.quiz.explanation}</p>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevStep}
          disabled={currentStep === 0}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Previous
        </Button>
        
        <Button
          onClick={handleNextStep}
          disabled={currentStepData.quiz && quizAnswers[currentStep] === undefined}
          className="flex items-center gap-2"
        >
          {isLastStep ? "Complete Tutorial" : "Next"}
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
});
