
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, ArrowLeft, ArrowRight, X, TrendingUp, BarChart3, Activity } from 'lucide-react';
import { TechnicalIndicatorsDemo } from './visualizations/TechnicalIndicatorsDemo';

interface TechnicalIndicatorsTutorialProps {
  onComplete: () => void;
  onExit: () => void;
}

interface Step {
  id: number;
  title: string;
  content: React.ReactNode;
  hasQuiz?: boolean;
  quizQuestion?: string;
  quizOptions?: string[];
  correctAnswer?: number;
}

export const TechnicalIndicatorsTutorial = React.memo(function TechnicalIndicatorsTutorial({
  onComplete,
  onExit
}: TechnicalIndicatorsTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [quizAnswers, setQuizAnswers] = useState<Record<number, number>>({});
  const [showQuizResult, setShowQuizResult] = useState<Record<number, boolean>>({});

  const steps: Step[] = [
    {
      id: 0,
      title: "Introduction to Technical Indicators",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="h-8 w-8 text-blue-600" />
            <div>
              <h3 className="text-xl font-semibold">Welcome to Technical Indicators</h3>
              <p className="text-muted-foreground">Master the tools that help predict price movements</p>
            </div>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">What You'll Learn:</h4>
            <ul className="space-y-2 text-blue-700">
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Moving Averages (SMA, EMA)
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Relative Strength Index (RSI)
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                MACD (Moving Average Convergence Divergence)
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Volume Analysis
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                How to combine indicators effectively
              </li>
            </ul>
          </div>

          <div className="bg-amber-50 p-4 rounded-lg">
            <h4 className="font-semibold text-amber-800 mb-2">Important Note:</h4>
            <p className="text-amber-700">
              Technical indicators are tools to help analyze price movements, but they're not foolproof. 
              Always use multiple indicators and consider fundamental analysis too.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 1,
      title: "Moving Averages - The Foundation",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <Activity className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-semibold">Understanding Moving Averages</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Simple Moving Average (SMA)</h4>
              <p className="text-green-700 text-sm mb-2">
                Calculates the average price over a specific number of periods.
              </p>
              <div className="bg-white p-2 rounded text-sm">
                <strong>Formula:</strong> (P1 + P2 + ... + Pn) / n
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Exponential Moving Average (EMA)</h4>
              <p className="text-blue-700 text-sm mb-2">
                Gives more weight to recent prices, making it more responsive.
              </p>
              <div className="bg-white p-2 rounded text-sm">
                <strong>Key:</strong> Reacts faster to price changes
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Common Moving Average Periods:</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
              <Badge variant="outline">5-day (Very Short)</Badge>
              <Badge variant="outline">20-day (Short)</Badge>
              <Badge variant="outline">50-day (Medium)</Badge>
              <Badge variant="outline">200-day (Long)</Badge>
            </div>
            <p className="text-muted-foreground text-sm mt-2">
              Shorter periods = more sensitive to price changes
            </p>
          </div>

          <div className="bg-indigo-50 p-4 rounded-lg">
            <h4 className="font-semibold text-indigo-800 mb-2">Trading Signals:</h4>
            <ul className="space-y-1 text-indigo-700 text-sm">
              <li>• <strong>Golden Cross:</strong> Short MA crosses above long MA (bullish)</li>
              <li>• <strong>Death Cross:</strong> Short MA crosses below long MA (bearish)</li>
              <li>• <strong>Price vs MA:</strong> Price above MA suggests uptrend</li>
            </ul>
          </div>
        </div>
      ),
      hasQuiz: true,
      quizQuestion: "What does it typically mean when a stock price crosses above its 50-day moving average?",
      quizOptions: [
        "It's a bearish signal indicating the stock will fall",
        "It's a bullish signal suggesting potential upward momentum",
        "It has no significance for trading decisions",
        "It means the stock is overvalued"
      ],
      correctAnswer: 1
    },
    {
      id: 2,
      title: "RSI - Measuring Momentum",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold">Relative Strength Index (RSI)</h3>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">What is RSI?</h4>
            <p className="text-purple-700 text-sm">
              RSI measures the speed and magnitude of price changes, oscillating between 0 and 100. 
              It helps identify overbought and oversold conditions.
            </p>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="bg-green-50 p-3 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-600">0-30</div>
              <div className="text-sm font-semibold text-green-800">Oversold</div>
              <p className="text-xs text-green-600 mt-1">Potential buying opportunity</p>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg text-center">
              <div className="text-2xl font-bold text-gray-600">30-70</div>
              <div className="text-sm font-semibold text-gray-800">Normal Range</div>
              <p className="text-xs text-gray-600 mt-1">No extreme condition</p>
            </div>
            <div className="bg-red-50 p-3 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-600">70-100</div>
              <div className="text-sm font-semibold text-red-800">Overbought</div>
              <p className="text-xs text-red-600 mt-1">Potential selling opportunity</p>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">RSI Trading Strategies:</h4>
            <ul className="space-y-1 text-yellow-700 text-sm">
              <li>• <strong>Divergence:</strong> Price makes new high/low but RSI doesn't confirm</li>
              <li>• <strong>Centerline Crossover:</strong> RSI crossing above/below 50</li>
              <li>• <strong>Failure Swings:</strong> RSI fails to exceed previous high/low</li>
            </ul>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Important Considerations:</h4>
            <p className="text-blue-700 text-sm">
              RSI can remain overbought or oversold for extended periods during strong trends. 
              Always combine with other indicators and trend analysis.
            </p>
          </div>
        </div>
      ),
      hasQuiz: true,
      quizQuestion: "An RSI reading of 25 typically suggests:",
      quizOptions: [
        "The stock is overbought and due for a decline",
        "The stock is in normal trading range",
        "The stock is oversold and might be due for a bounce",
        "The RSI indicator is broken"
      ],
      correctAnswer: 2
    },
    {
      id: 3,
      title: "MACD - Trend and Momentum",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="h-6 w-6 text-indigo-600" />
            <h3 className="text-lg font-semibold">Moving Average Convergence Divergence (MACD)</h3>
          </div>

          <div className="bg-indigo-50 p-4 rounded-lg">
            <h4 className="font-semibold text-indigo-800 mb-2">MACD Components:</h4>
            <div className="space-y-2 text-indigo-700 text-sm">
              <p><strong>MACD Line:</strong> 12-day EMA minus 26-day EMA</p>
              <p><strong>Signal Line:</strong> 9-day EMA of the MACD line</p>
              <p><strong>Histogram:</strong> MACD line minus Signal line</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Bullish Signals</h4>
              <ul className="space-y-1 text-green-700 text-sm">
                <li>• MACD crosses above Signal line</li>
                <li>• MACD crosses above zero line</li>
                <li>• Bullish divergence with price</li>
                <li>• Histogram turns positive</li>
              </ul>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Bearish Signals</h4>
              <ul className="space-y-1 text-red-700 text-sm">
                <li>• MACD crosses below Signal line</li>
                <li>• MACD crosses below zero line</li>
                <li>• Bearish divergence with price</li>
                <li>• Histogram turns negative</li>
              </ul>
            </div>
          </div>

          <div className="bg-amber-50 p-4 rounded-lg">
            <h4 className="font-semibold text-amber-800 mb-2">MACD Divergence:</h4>
            <p className="text-amber-700 text-sm mb-2">
              One of the most powerful MACD signals occurs when price makes a new high/low 
              but MACD fails to confirm with a new high/low.
            </p>
            <div className="bg-white p-2 rounded text-sm">
              <strong>Example:</strong> Stock hits new high, but MACD makes lower high = potential reversal
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">MACD Settings:</h4>
            <p className="text-muted-foreground text-sm">
              Default settings (12, 26, 9) work well for daily charts. 
              Shorter settings make MACD more sensitive but generate more false signals.
            </p>
          </div>
        </div>
      ),
      hasQuiz: true,
      quizQuestion: "What does it typically mean when the MACD line crosses above the signal line?",
      quizOptions: [
        "It's a bearish signal indicating potential downward momentum",
        "It's a bullish signal suggesting potential upward momentum",
        "It indicates the stock is overvalued",
        "It has no trading significance"
      ],
      correctAnswer: 1
    },
    {
      id: 4,
      title: "Volume Analysis",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <BarChart3 className="h-6 w-6 text-orange-600" />
            <h3 className="text-lg font-semibold">Understanding Volume in Technical Analysis</h3>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800 mb-2">Why Volume Matters:</h4>
            <p className="text-orange-700 text-sm">
              Volume confirms price movements. High volume during price moves suggests strong conviction, 
              while low volume may indicate weak or unsustainable moves.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Volume Confirmation</h4>
              <ul className="space-y-1 text-green-700 text-sm">
                <li>• Rising prices + High volume = Strong uptrend</li>
                <li>• Falling prices + High volume = Strong downtrend</li>
                <li>• Breakouts + High volume = Valid breakout</li>
              </ul>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Volume Warning Signs</h4>
              <ul className="space-y-1 text-red-700 text-sm">
                <li>• Rising prices + Low volume = Weak rally</li>
                <li>• Breakout + Low volume = False breakout</li>
                <li>• Volume declining = Trend losing steam</li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Volume Indicators:</h4>
            <div className="space-y-2 text-blue-700 text-sm">
              <p><strong>Volume Moving Average:</strong> Smooths out volume spikes</p>
              <p><strong>Volume Rate of Change:</strong> Measures volume momentum</p>
              <p><strong>On-Balance Volume (OBV):</strong> Cumulative volume indicator</p>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">Volume Patterns:</h4>
            <ul className="space-y-1 text-purple-700 text-sm">
              <li>• <strong>Volume Spike:</strong> Unusual high volume often signals important events</li>
              <li>• <strong>Volume Dry Up:</strong> Very low volume may precede a breakout</li>
              <li>• <strong>Volume Climax:</strong> Extremely high volume may mark trend exhaustion</li>
            </ul>
          </div>
        </div>
      ),
      hasQuiz: true,
      quizQuestion: "What does high volume during a price breakout typically indicate?",
      quizOptions: [
        "The breakout is likely false and will reverse",
        "The breakout is more likely to be valid and sustainable",
        "Volume has no impact on breakout reliability",
        "The stock is about to crash"
      ],
      correctAnswer: 1
    },
    {
      id: 5,
      title: "Interactive Technical Analysis",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <Activity className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold">Practice with Live Indicators</h3>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg mb-4">
            <h4 className="font-semibold text-blue-800 mb-2">Interactive Demo:</h4>
            <p className="text-blue-700 text-sm">
              Use the interactive chart below to explore different technical indicators. 
              Click on each indicator to see how they work and what signals they generate.
            </p>
          </div>

          <TechnicalIndicatorsDemo />

          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">Practice Tips:</h4>
            <ul className="space-y-1 text-green-700 text-sm">
              <li>• Try each indicator tab to see different analysis tools</li>
              <li>• Notice how RSI levels correspond to potential trading signals</li>
              <li>• Observe MACD crossovers and what they might suggest</li>
              <li>• Compare volume levels with price movements</li>
              <li>• Look for patterns and confirmations between indicators</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 6,
      title: "Combining Indicators Effectively",
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="h-6 w-6 text-emerald-600" />
            <h3 className="text-lg font-semibold">Multi-Indicator Analysis</h3>
          </div>

          <div className="bg-emerald-50 p-4 rounded-lg">
            <h4 className="font-semibold text-emerald-800 mb-2">Why Combine Indicators?</h4>
            <p className="text-emerald-700 text-sm">
              No single indicator is perfect. Combining different types of indicators helps 
              reduce false signals and increases confidence in trading decisions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Trend Indicators</h4>
              <ul className="space-y-1 text-blue-700 text-sm">
                <li>• Moving Averages</li>
                <li>• MACD</li>
                <li>• Trend Lines</li>
              </ul>
              <p className="text-xs text-blue-600 mt-2">Show direction of trend</p>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-800 mb-2">Momentum Indicators</h4>
              <ul className="space-y-1 text-purple-700 text-sm">
                <li>• RSI</li>
                <li>• Stochastic</li>
                <li>• ROC</li>
              </ul>
              <p className="text-xs text-purple-600 mt-2">Show strength of trend</p>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-800 mb-2">Volume Indicators</h4>
              <ul className="space-y-1 text-orange-700 text-sm">
                <li>• Volume MA</li>
                <li>• OBV</li>
                <li>• Volume Oscillator</li>
              </ul>
              <p className="text-xs text-orange-600 mt-2">Confirm price moves</p>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">Effective Combinations:</h4>
            <div className="space-y-2 text-yellow-700 text-sm">
              <p><strong>Trend + Momentum:</strong> MA crossover + RSI confirmation</p>
              <p><strong>Trend + Volume:</strong> Breakout + Volume spike</p>
              <p><strong>Multiple Timeframes:</strong> Weekly trend + Daily entry signals</p>
            </div>
          </div>

          <div className="bg-red-50 p-4 rounded-lg">
            <h4 className="font-semibold text-red-800 mb-2">Common Mistakes to Avoid:</h4>
            <ul className="space-y-1 text-red-700 text-sm">
              <li>• Using too many indicators (analysis paralysis)</li>
              <li>• Ignoring the overall trend</li>
              <li>• Not considering market context</li>
              <li>• Relying solely on technical analysis</li>
              <li>• Not adapting to changing market conditions</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Best Practices:</h4>
            <ul className="space-y-1 text-muted-foreground text-sm">
              <li>• Start with 2-3 indicators maximum</li>
              <li>• Use different types (trend, momentum, volume)</li>
              <li>• Wait for confirmation from multiple indicators</li>
              <li>• Consider the bigger picture and market sentiment</li>
              <li>• Practice with paper trading before risking real money</li>
            </ul>
          </div>
        </div>
      ),
      hasQuiz: true,
      quizQuestion: "What's the main benefit of combining different types of technical indicators?",
      quizOptions: [
        "It guarantees profitable trades",
        "It reduces false signals and increases confidence in analysis",
        "It makes charts look more complex and professional",
        "It eliminates the need for fundamental analysis"
      ],
      correctAnswer: 1
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(currentStep + 1);
    } else {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleQuizAnswer = (stepId: number, answerIndex: number) => {
    setQuizAnswers(prev => ({ ...prev, [stepId]: answerIndex }));
    setShowQuizResult(prev => ({ ...prev, [stepId]: true }));
  };

  const isQuizCorrect = (stepId: number) => {
    const step = steps.find(s => s.id === stepId);
    return step?.correctAnswer === quizAnswers[stepId];
  };

  const progress = ((completedSteps.size) / steps.length) * 100;
  const currentStepData = steps[currentStep];

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onExit}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Technical Indicators Tutorial</h1>
            <p className="text-muted-foreground">Master moving averages, RSI, MACD, and other popular indicators</p>
          </div>
        </div>
        <Badge variant="secondary" className="text-sm">
          Intermediate Level
        </Badge>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium">Progress</span>
          <span className="text-sm text-muted-foreground">
            {completedSteps.size} of {steps.length} completed
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Step Content */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <span className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold">
                {currentStep + 1}
              </span>
              {currentStepData.title}
            </CardTitle>
            {completedSteps.has(currentStep) && (
              <CheckCircle className="h-5 w-5 text-green-600" />
            )}
          </div>
        </CardHeader>
        <CardContent>
          {currentStepData.content}

          {/* Quiz Section */}
          {currentStepData.hasQuiz && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold mb-3">📝 Quick Check:</h4>
              <p className="mb-4">{currentStepData.quizQuestion}</p>
              <div className="space-y-2">
                {currentStepData.quizOptions?.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuizAnswer(currentStepData.id, index)}
                    disabled={showQuizResult[currentStepData.id]}
                    className={`w-full text-left p-3 rounded border transition-colors ${
                      showQuizResult[currentStepData.id]
                        ? index === currentStepData.correctAnswer
                          ? 'bg-green-100 border-green-300 text-green-800'
                          : quizAnswers[currentStepData.id] === index
                          ? 'bg-red-100 border-red-300 text-red-800'
                          : 'bg-white border-gray-200'
                        : 'bg-white border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
              {showQuizResult[currentStepData.id] && (
                <div className={`mt-3 p-3 rounded ${
                  isQuizCorrect(currentStepData.id) 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {isQuizCorrect(currentStepData.id) 
                    ? '✅ Correct! Well done.' 
                    : '❌ Not quite right. The correct answer is highlighted above.'}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Previous
        </Button>
        <Button
          onClick={handleNext}
          className="flex items-center gap-2"
        >
          {currentStep === steps.length - 1 ? 'Complete Tutorial' : 'Next'}
          {currentStep < steps.length - 1 && <ArrowRight className="h-4 w-4" />}
        </Button>
      </div>
    </div>
  );
});
