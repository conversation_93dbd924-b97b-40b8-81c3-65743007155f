
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface ScoreIndicatorProps {
  score: number;
  label: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function ScoreIndicator({ 
  score, 
  label, 
  showLabel = true, 
  size = 'md' 
}: ScoreIndicatorProps) {
  const getColorClass = () => {
    if (score >= 80) return "bg-success";
    if (score >= 60) return "bg-warning";
    return "bg-danger";
  };
  
  const heightClass = {
    'sm': 'h-1',
    'md': 'h-2',
    'lg': 'h-3'
  }[size];

  // Instead of using indicatorClassName, we'll modify the Progress component itself
  return (
    <div className="space-y-1">
      {showLabel && (
        <div className="flex justify-between text-xs">
          <span>{label}</span>
          <span className="font-medium">{score}/100</span>
        </div>
      )}
      <Progress 
        value={score} 
        className={`${heightClass} w-full`} 
      >
        <div 
          className={cn("h-full", getColorClass())} 
          style={{ width: `${score}%` }}
        />
      </Progress>
    </div>
  );
}
