
import { mockNews, getPredictions } from '@/utils/stockApi';
import type { Stock } from '@/utils/stockApi';
import type { MarketStatsData } from '@/components/dashboard/MarketStats';
import { stockService } from './stockService';
import { newsService } from './newsService';

interface DashboardServiceData {
  news: typeof mockNews;
  marketStats: MarketStatsData;
  predictions: ReturnType<typeof getPredictions>;
  historicalPrices: Array<{ date: string; price: number }>;
}

export const getMarketStats = (): MarketStatsData => {
  try {
    return {
      totalMarketCap: "$42.18T",
      tradingVolume: "2.64B",
      topGainer: {
        symbol: "NVDA",
        name: "NVIDIA Corp",
        change: 2.01
      },
      topLoser: {
        symbol: "TSLA", 
        name: "Tesla Inc",
        change: -1.35
      }
    };
  } catch (error) {
    console.error('Error getting market stats:', error);
    throw new Error('Failed to fetch market statistics');
  }
};

export const getDashboardData = async (selectedSymbol: string, stocks: Stock[]): Promise<DashboardServiceData> => {
  try {
    if (!selectedSymbol) {
      throw new Error('Selected symbol is required');
    }

    if (!stocks || stocks.length === 0) {
      throw new Error('No stocks data available');
    }

    // Use stockService to get stock data with historical prices
    const stockData = await stockService.getStockData(stocks, {
      symbol: selectedSymbol,
      includeHistory: true,
      historyDays: 30
    });

    if (!stockData.stock) {
      throw new Error(`Stock with symbol ${selectedSymbol} not found`);
    }

    // Use newsService to get relevant news
    const relevantNews = newsService.getStockNews(mockNews, selectedSymbol, 10);

    return {
      news: relevantNews.length > 0 ? relevantNews : mockNews.slice(0, 5),
      marketStats: getMarketStats(),
      predictions: getPredictions(selectedSymbol),
      historicalPrices: stockData.historicalPrices || []
    };
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    throw error;
  }
};

export { mockNews, getPredictions, generatePriceHistory };
export type { MarketStatsData, DashboardServiceData };
