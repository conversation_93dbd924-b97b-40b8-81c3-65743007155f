
import { FinancialData, BalanceSheetData, CashFlowData } from '@/utils/stockApi';

export const useChartData = (
  financialData: FinancialData[],
  balanceSheetData: BalanceSheetData[],
  cashFlowData: CashFlowData[],
  stockSymbol: string
) => {
  const revenueData = financialData.map(item => ({
    period: item.period,
    revenue: item.revenue / 1e9,
    netIncome: item.netIncome / 1e9
  })).sort((a, b) => a.period.localeCompare(b.period));
  
  const marginData = financialData.map(item => ({
    period: item.period,
    grossMargin: item.grossMargin,
    operatingMargin: item.operatingMargin,
    netMargin: item.netMargin
  })).sort((a, b) => a.period.localeCompare(b.period));
  
  const cashFlowChartData = cashFlowData.map(item => ({
    period: item.period,
    operating: item.operatingCashFlow / 1e9,
    investing: item.investingCashFlow / 1e9,
    financing: item.financingCashFlow / 1e9,
    freeCashFlow: item.freeCashFlow / 1e9
  })).sort((a, b) => a.period.localeCompare(b.period));
  
  const balanceSheetChartData = balanceSheetData.map(item => ({
    period: item.period,
    assets: item.totalAssets / 1e9,
    liabilities: item.totalLiabilities / 1e9,
    equity: item.totalEquity / 1e9
  })).sort((a, b) => a.period.localeCompare(b.period));

  const peerComparisonData = [
    {
      name: 'Revenue Growth',
      [stockSymbol]: 4.1,
      'Peer Avg': 3.2,
      'Industry Avg': 2.8
    },
    {
      name: 'Profit Margin',
      [stockSymbol]: 24.6,
      'Peer Avg': 21.5,
      'Industry Avg': 18.3
    },
    {
      name: 'P/E Ratio',
      [stockSymbol]: 28.5,
      'Peer Avg': 25.2,
      'Industry Avg': 22.6
    },
    {
      name: 'ROE',
      [stockSymbol]: 156.0,
      'Peer Avg': 32.5,
      'Industry Avg': 24.7
    },
    {
      name: 'Debt/Equity',
      [stockSymbol]: 1.7,
      'Peer Avg': 1.2,
      'Industry Avg': 0.9
    }
  ];

  return {
    revenueData,
    marginData,
    cashFlowChartData,
    balanceSheetChartData,
    peerComparisonData
  };
};
