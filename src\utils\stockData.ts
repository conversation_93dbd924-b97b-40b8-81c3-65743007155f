import { useState, useEffect } from 'react';
import type { Stock } from '@/types/stock';

// Mock data for popular stocks
export const mockStocks: Stock[] = [
  {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    price: 187.32,
    change: 1.28,
    changePercent: 0.69,
    volume: 58394210,
    marketCap: 2920000000000,
    pe: 28.5,
    dividend: 0.96,
    eps: 6.14,
    high52: 198.23,
    low52: 143.90,
    beta: 1.28,
    lastUpdated: new Date()
  },
  {
    symbol: 'MSFT',
    name: 'Microsoft Corp.',
    price: 402.65,
    change: 3.71,
    changePercent: 0.93,
    volume: 22154780,
    marketCap: 2990000000000,
    pe: 32.8,
    dividend: 2.72,
    eps: 11.58,
    high52: 415.32,
    low52: 310.65,
    beta: 0.89,
    lastUpdated: new Date()
  },
  {
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    price: 157.95,
    change: -0.63,
    changePercent: -0.40,
    volume: 18729340,
    marketCap: 1980000000000,
    pe: 24.1,
    dividend: 0,
    eps: 6.24,
    high52: 169.45,
    low52: 114.25,
    beta: 1.04,
    lastUpdated: new Date()
  },
  {
    symbol: 'AMZN',
    name: 'Amazon.com Inc.',
    price: 179.83,
    change: 1.02,
    changePercent: 0.57,
    volume: 27194600,
    marketCap: 1870000000000,
    pe: 60.2,
    dividend: 0,
    eps: 2.82,
    high52: 185.10,
    low52: 118.35,
    beta: 1.14,
    lastUpdated: new Date()
  },
  {
    symbol: 'NVDA',
    name: 'NVIDIA Corp.',
    price: 950.02,
    change: 18.75,
    changePercent: 2.01,
    volume: 42638210,
    marketCap: 2340000000000,
    pe: 78.5,
    dividend: 0.16,
    eps: 12.15,
    high52: 974.35,
    low52: 498.22,
    beta: 1.65,
    lastUpdated: new Date()
  },
  {
    symbol: 'TSLA',
    name: 'Tesla Inc.',
    price: 237.47,
    change: -3.25,
    changePercent: -1.35,
    volume: 67129580,
    marketCap: 756000000000,
    pe: 55.8,
    dividend: 0,
    eps: 4.30,
    high52: 299.29,
    low52: 152.37,
    beta: 2.01,
    lastUpdated: new Date()
  },
  {
    symbol: 'META',
    name: 'Meta Platforms Inc.',
    price: 474.99,
    change: 5.12,
    changePercent: 1.09,
    volume: 15283940,
    marketCap: 1215000000000,
    pe: 27.3,
    dividend: 0,
    eps: 16.78,
    high52: 531.49,
    low52: 286.90,
    beta: 1.32,
    lastUpdated: new Date()
  },
  {
    symbol: 'V',
    name: 'Visa Inc.',
    price: 267.80,
    change: -1.05,
    changePercent: -0.39,
    volume: 8943760,
    marketCap: 548000000000,
    pe: 31.7,
    dividend: 2.08,
    eps: 8.46,
    high52: 290.96,
    low52: 245.75,
    beta: 0.89,
    lastUpdated: new Date()
  }
];

// NOTE: useStockData hook is now exported from stocksApi.ts to avoid conflicts
// This function is kept for reference but not exported

// NOTE: getStockBySymbol and generatePriceHistory are now exported from stocksApi.ts to avoid conflicts
