
import { 
  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, 
  <PERSON>lt<PERSON>, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface MarginsChartProps {
  data: {
    period: string;
    grossMargin: number;
    operatingMargin: number;
    netMargin: number;
  }[];
}

export const MarginsChart = ({ data }: MarginsChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profit Margins (%)</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="period" />
            <YAxis domain={[0, 'dataMax + 10']} />
            <Tooltip 
              formatter={(value: number) => [`${value.toFixed(2)}%`, '']}
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="grossMargin" 
              name="Gross Margin" 
              stroke="hsl(var(--primary))"
              strokeWidth={2}
            />
            <Line 
              type="monotone" 
              dataKey="operatingMargin" 
              name="Operating Margin" 
              stroke="hsl(var(--warning))"
              strokeWidth={2}
            />
            <Line 
              type="monotone" 
              dataKey="netMargin" 
              name="Net Margin" 
              stroke="hsl(var(--success))"
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
