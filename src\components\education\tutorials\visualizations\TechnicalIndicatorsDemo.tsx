
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';

const generateTechnicalData = () => {
  const data = [];
  let price = 100;
  
  for (let i = 0; i < 20; i++) {
    price += (Math.random() - 0.5) * 4;
    const volume = Math.floor(Math.random() * 2000) + 1000;
    
    // Simple Moving Average (last 5 periods)
    const sma5 = i >= 4 ? 
      data.slice(i-4).reduce((sum, d) => sum + d.price, 0) / 5 + price / 5 : price;
    
    // RSI calculation (simplified)
    const rsi = 30 + Math.random() * 40; // Simplified RSI between 30-70
    
    // MACD (simplified)
    const macd = (Math.random() - 0.5) * 2;
    const signal = macd * 0.8;
    
    data.push({
      day: i + 1,
      price: Math.round(price * 100) / 100,
      sma5: Math.round(sma5 * 100) / 100,
      volume,
      rsi: Math.round(rsi * 100) / 100,
      macd: Math.round(macd * 100) / 100,
      signal: Math.round(signal * 100) / 100,
      histogram: Math.round((macd - signal) * 100) / 100
    });
  }
  
  return data;
};

export const TechnicalIndicatorsDemo = React.memo(function TechnicalIndicatorsDemo() {
  const [activeIndicator, setActiveIndicator] = useState('price');
  const [data] = useState(generateTechnicalData());

  const indicators = [
    { id: 'price', name: 'Price & SMA', description: 'Price with Simple Moving Average' },
    { id: 'rsi', name: 'RSI', description: 'Relative Strength Index (0-100)' },
    { id: 'macd', name: 'MACD', description: 'Moving Average Convergence Divergence' },
    { id: 'volume', name: 'Volume', description: 'Trading Volume Analysis' }
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-semibold">{`Day ${label}`}</p>
          {activeIndicator === 'price' && (
            <>
              <p className="text-blue-600">{`Price: $${data.price}`}</p>
              <p className="text-green-600">{`SMA(5): $${data.sma5}`}</p>
            </>
          )}
          {activeIndicator === 'rsi' && (
            <p className="text-purple-600">{`RSI: ${data.rsi}`}</p>
          )}
          {activeIndicator === 'macd' && (
            <>
              <p className="text-blue-600">{`MACD: ${data.macd}`}</p>
              <p className="text-red-600">{`Signal: ${data.signal}`}</p>
              <p className="text-green-600">{`Histogram: ${data.histogram}`}</p>
            </>
          )}
          {activeIndicator === 'volume' && (
            <p className="text-orange-600">{`Volume: ${data.volume.toLocaleString()}`}</p>
          )}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    switch (activeIndicator) {
      case 'price':
        return (
          <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="day" stroke="#666" fontSize={12} />
            <YAxis stroke="#666" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Line 
              type="monotone" 
              dataKey="price" 
              stroke="#3b82f6" 
              strokeWidth={2}
              dot={false}
              name="Price"
            />
            <Line 
              type="monotone" 
              dataKey="sma5" 
              stroke="#10b981" 
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={false}
              name="SMA(5)"
            />
          </ComposedChart>
        );

      case 'rsi':
        return (
          <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="day" stroke="#666" fontSize={12} />
            <YAxis domain={[0, 100]} stroke="#666" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={70} stroke="#ef4444" strokeDasharray="3 3" />
            <ReferenceLine y={30} stroke="#10b981" strokeDasharray="3 3" />
            <ReferenceLine y={50} stroke="#6b7280" strokeDasharray="1 1" />
            <Line 
              type="monotone" 
              dataKey="rsi" 
              stroke="#8b5cf6" 
              strokeWidth={2}
              dot={false}
              name="RSI"
            />
          </ComposedChart>
        );

      case 'macd':
        return (
          <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="day" stroke="#666" fontSize={12} />
            <YAxis stroke="#666" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={0} stroke="#6b7280" strokeDasharray="1 1" />
            <Bar dataKey="histogram" fill="#10b981" fillOpacity={0.6} name="Histogram" />
            <Line 
              type="monotone" 
              dataKey="macd" 
              stroke="#3b82f6" 
              strokeWidth={2}
              dot={false}
              name="MACD"
            />
            <Line 
              type="monotone" 
              dataKey="signal" 
              stroke="#ef4444" 
              strokeWidth={2}
              dot={false}
              name="Signal"
            />
          </ComposedChart>
        );

      case 'volume':
        return (
          <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis dataKey="day" stroke="#666" fontSize={12} />
            <YAxis stroke="#666" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="volume" 
              fill="#f59e0b" 
              fillOpacity={0.7}
              name="Volume"
            />
          </ComposedChart>
        );

      default:
        return null;
    }
  };

  const getIndicatorSignal = () => {
    const lastData = data[data.length - 1];
    
    switch (activeIndicator) {
      case 'price':
        return lastData.price > lastData.sma5 ? 
          { signal: 'Bullish', color: 'text-green-600', description: 'Price above moving average' } :
          { signal: 'Bearish', color: 'text-red-600', description: 'Price below moving average' };
      
      case 'rsi':
        if (lastData.rsi > 70) {
          return { signal: 'Overbought', color: 'text-red-600', description: 'Consider selling pressure' };
        } else if (lastData.rsi < 30) {
          return { signal: 'Oversold', color: 'text-green-600', description: 'Consider buying opportunity' };
        } else {
          return { signal: 'Neutral', color: 'text-gray-600', description: 'No extreme condition' };
        }
      
      case 'macd':
        return lastData.macd > lastData.signal ?
          { signal: 'Bullish', color: 'text-green-600', description: 'MACD above signal line' } :
          { signal: 'Bearish', color: 'text-red-600', description: 'MACD below signal line' };
      
      case 'volume':
        const avgVolume = data.reduce((sum, d) => sum + d.volume, 0) / data.length;
        return lastData.volume > avgVolume * 1.2 ?
          { signal: 'High Volume', color: 'text-blue-600', description: 'Above average trading activity' } :
          { signal: 'Normal Volume', color: 'text-gray-600', description: 'Regular trading activity' };
      
      default:
        return { signal: 'Neutral', color: 'text-gray-600', description: '' };
    }
  };

  const currentSignal = getIndicatorSignal();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Technical Indicators Interactive Demo</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Indicator Selector */}
        <div className="flex flex-wrap gap-2">
          {indicators.map((indicator) => (
            <Button
              key={indicator.id}
              variant={activeIndicator === indicator.id ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveIndicator(indicator.id)}
            >
              {indicator.name}
            </Button>
          ))}
        </div>

        {/* Current Indicator Description */}
        <div className="p-3 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-800">
            {indicators.find(i => i.id === activeIndicator)?.name}
          </h4>
          <p className="text-sm text-blue-700">
            {indicators.find(i => i.id === activeIndicator)?.description}
          </p>
        </div>

        {/* Chart */}
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>

        {/* Current Signal */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="font-semibold">Current Signal:</h4>
            <p className="text-sm text-muted-foreground">{currentSignal.description}</p>
          </div>
          <Badge 
            variant={currentSignal.signal.includes('Bullish') ? 'default' : 
                    currentSignal.signal.includes('Bearish') ? 'destructive' : 'secondary'}
            className={currentSignal.color}
          >
            {currentSignal.signal}
          </Badge>
        </div>

        {/* Indicator-specific explanations */}
        {activeIndicator === 'rsi' && (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-green-50 rounded-lg">
              <h5 className="font-semibold text-green-800">RSI &lt; 30</h5>
              <p className="text-sm text-green-700">Oversold</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <h5 className="font-semibold text-gray-800">30 &lt; RSI &lt; 70</h5>
              <p className="text-sm text-gray-700">Normal</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <h5 className="font-semibold text-red-800">RSI &gt; 70</h5>
              <p className="text-sm text-red-700">Overbought</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});
