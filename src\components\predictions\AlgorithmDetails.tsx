
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export function AlgorithmDetails() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Algorithm Selection Guide</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <section>
            <h3 className="font-semibold mb-2">Time Horizon Guide</h3>
            <div className="space-y-2">
              <div>
                <Badge variant="outline">1-6 months</Badge>
                <p className="text-sm mt-1">Best: Gradient Boosting, Temporal CNN</p>
              </div>
              <div>
                <Badge variant="outline">6-12 months</Badge>
                <p className="text-sm mt-1">Best: Transformer Models, LSTM Networks</p>
              </div>
              <div>
                <Badge variant="outline">12-24 months</Badge>
                <p className="text-sm mt-1">Best: Ensemble Methods, GNN</p>
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-semibold mb-2">Algorithm Types</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge>Deep Learning</Badge>
                <span className="text-sm text-muted-foreground">6 algorithms</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">Machine Learning</Badge>
                <span className="text-sm text-muted-foreground">2 algorithms</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Hybrid</Badge>
                <span className="text-sm text-muted-foreground">2 algorithms</span>
              </div>
            </div>
          </section>

          <section>
            <h3 className="font-semibold mb-2">Accuracy Ratings</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge>Very High</Badge>
                <span className="text-sm text-muted-foreground">4 algorithms</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">High</Badge>
                <span className="text-sm text-muted-foreground">6 algorithms</span>
              </div>
            </div>
          </section>
        </div>
      </CardContent>
    </Card>
  );
}
