
import React from 'react';
import { StockMarketIntroTutorial } from './tutorials/StockMarketIntroTutorial';
import { StockChartsReadingTutorial } from './tutorials/StockChartsReadingTutorial';
import { TechnicalIndicatorsTutorial } from './tutorials/TechnicalIndicatorsTutorial';
import { OptionsStrategiesTutorial } from './tutorials/OptionsStrategiesTutorial';

interface TutorialRouterProps {
  tutorialId: string;
  onComplete: () => void;
  onExit: () => void;
}

export const TutorialRouter = React.memo(function TutorialRouter({
  tutorialId,
  onComplete,
  onExit
}: TutorialRouterProps) {
  switch (tutorialId) {
    case 'basics-1':
      return (
        <StockMarketIntroTutorial 
          onComplete={onComplete}
          onExit={onExit}
        />
      );
    
    case 'basics-2':
      return (
        <StockChartsReadingTutorial 
          onComplete={onComplete}
          onExit={onExit}
        />
      );

    case 'technical-1':
      return (
        <TechnicalIndicatorsTutorial 
          onComplete={onComplete}
          onExit={onExit}
        />
      );

    case 'advanced-1':
      return (
        <OptionsStrategiesTutorial 
          onComplete={onComplete}
          onExit={onExit}
        />
      );
    
    default:
      return (
        <div className="text-center p-8">
          <h2 className="text-xl font-semibold mb-2">Tutorial Not Found</h2>
          <p className="text-muted-foreground">
            The tutorial "{tutorialId}" is not yet available.
          </p>
        </div>
      );
  }
});
