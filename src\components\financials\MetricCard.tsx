
import { Card, CardContent } from '@/components/ui/card';
import { MetricCardProps } from './types';

export function MetricCard({ title, value, trend, status }: MetricCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <h3 className="text-sm font-medium mb-2">{title}</h3>
        <div className="flex items-center justify-between">
          <span className="text-lg font-semibold">{value}</span>
          {trend && (
            <span className={`text-xs ${status}`}>{trend}</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
