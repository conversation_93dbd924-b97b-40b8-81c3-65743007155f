
import { 
  Bar<PERSON>hart, Bar, Line, XAxis, YAxis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, ComposedChart,
  ReferenceLine
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface RevenueChartProps {
  data: {
    period: string;
    revenue: number;
    netIncome: number;
  }[];
}

export const RevenueChart = ({ data }: RevenueChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Revenue and Net Income (Billions)</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={data}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="period" />
            <YAxis yAxisId="left" />
            <Tooltip 
              formatter={(value: number) => [`$${value.toFixed(2)}B`, '']}
            />
            <Legend />
            <ReferenceLine 
              y={0} 
              stroke="hsl(var(--border))" 
              strokeWidth={1}
              yAxisId="left"
            />
            <Bar 
              yAxisId="left" 
              dataKey="revenue" 
              fill="hsl(var(--primary))" 
              name="Revenue"
              barSize={60} 
            />
            <Line 
              yAxisId="left" 
              type="monotone" 
              dataKey="netIncome" 
              stroke="hsl(var(--success))"
              strokeWidth={2}
              name="Net Income" 
            />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
