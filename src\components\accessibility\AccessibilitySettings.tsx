
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { useAccessibility, TextSize } from '@/contexts/AccessibilityContext';
import { Accessibility } from 'lucide-react';

export function AccessibilitySettings() {
  const { settings, updateTextSize, toggleHighContrast, toggleScreenReader } = useAccessibility();

  const textSizeOptions: { value: TextSize; label: string; size: number }[] = [
    { value: 'small', label: 'A', size: 0 },
    { value: 'medium', label: 'AA', size: 33 },
    { value: 'large', label: 'AAA', size: 66 },
    { value: 'extra-large', label: 'AAAA', size: 100 },
  ];

  const getCurrentTextSizeValue = () => {
    const option = textSizeOptions.find(opt => opt.value === settings.textSize);
    return option ? option.size : 33;
  };

  const handleTextSizeChange = (value: number[]) => {
    const newValue = value[0];
    const option = textSizeOptions.reduce((prev, curr) => 
      Math.abs(curr.size - newValue) < Math.abs(prev.size - newValue) ? curr : prev
    );
    updateTextSize(option.value);
  };

  const getCurrentTextSizeLabel = () => {
    const option = textSizeOptions.find(opt => opt.value === settings.textSize);
    return option ? `${option.value.charAt(0).toUpperCase()}${option.value.slice(1)}` : 'Medium';
  };

  return (
    <Card className="bg-card/50 backdrop-blur border-border/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Accessibility className="h-5 w-5" />
          Accessibility Settings
        </CardTitle>
        <CardDescription>
          Customize accessibility features to improve your experience
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="text-size" className="text-base font-medium">
              Text Size
            </Label>
            <span className="text-sm text-muted-foreground">
              {getCurrentTextSizeLabel()}
            </span>
          </div>
          
          <div className="space-y-3">
            <Slider
              id="text-size"
              min={0}
              max={100}
              step={33}
              value={[getCurrentTextSizeValue()]}
              onValueChange={handleTextSizeChange}
              className="w-full"
              aria-label="Text size"
            />
            
            <div className="flex justify-between text-xs text-muted-foreground px-1">
              {textSizeOptions.map((option) => (
                <span key={option.value} className="font-mono">
                  {option.label}
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="high-contrast" className="text-base font-medium">
              High Contrast Mode
            </Label>
            <p className="text-sm text-muted-foreground">
              Increases contrast for better readability
            </p>
          </div>
          <Switch
            id="high-contrast"
            checked={settings.highContrastMode}
            onCheckedChange={toggleHighContrast}
            aria-label="Toggle high contrast mode"
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="screen-reader" className="text-base font-medium">
              Screen Reader Mode
            </Label>
            <p className="text-sm text-muted-foreground">
              Optimizes the site for screen readers
            </p>
          </div>
          <Switch
            id="screen-reader"
            checked={settings.screenReaderMode}
            onCheckedChange={toggleScreenReader}
            aria-label="Toggle screen reader mode"
          />
        </div>
      </CardContent>
    </Card>
  );
}
