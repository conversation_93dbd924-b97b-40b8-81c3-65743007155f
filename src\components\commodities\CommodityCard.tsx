
import { Commodity } from "@/types/commodities";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScoreIndicator } from "@/components/prospective/ScoreIndicator";
import { RiskBenefitIndicator } from "@/components/commodities/RiskBenefitIndicator";
import { cn } from "@/lib/utils";

interface CommodityCardProps {
  commodity: Commodity;
  onClick?: (commodity: Commodity) => void;
  isSelected?: boolean;
}

export function CommodityCard({ 
  commodity, 
  onClick,
  isSelected = false
}: CommodityCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(commodity);
    }
  };

  return (
    <Card 
      className={cn(
        "cursor-pointer hover:shadow-md transition-all",
        isSelected ? "ring-2 ring-primary" : ""
      )}
      onClick={handleClick}
    >
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-base flex items-center gap-2">
              {commodity.name}
              <Badge variant="outline" className="font-normal">
                {commodity.symbol}
              </Badge>
            </h3>
            <p className="text-sm text-muted-foreground">
              {commodity.category}
            </p>
          </div>
          <div className="text-right">
            <div className="font-bold">
              ${commodity.currentPrice.toFixed(2)}
              <span className="text-xs text-muted-foreground ml-1">
                /{commodity.unit}
              </span>
            </div>
            <div 
              className={cn(
                "text-xs font-medium",
                commodity.changePercent >= 0 ? "text-success" : "text-danger"
              )}
            >
              {commodity.changePercent >= 0 ? "+" : ""}
              {commodity.change.toFixed(2)} (
              {commodity.changePercent >= 0 ? "+" : ""}
              {commodity.changePercent.toFixed(2)}%)
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-2 space-y-3">
        <div className="grid grid-cols-2 gap-4 mt-2">
          <div>
            <p className="text-xs text-muted-foreground mb-1">Risk Level</p>
            <div className="flex items-center">
              <Badge 
                variant={
                  commodity.riskLevel === "Low" ? "success" :
                  commodity.riskLevel === "Moderate" ? "warning" :
                  commodity.riskLevel === "High" ? "destructive" :
                  "destructive" // for "Very High"
                }
                className={cn(
                  "rounded-sm",
                  commodity.riskLevel === "Very High" ? "bg-red-800" : ""
                )}
              >
                {commodity.riskLevel}
              </Badge>
            </div>
          </div>
          <div>
            <p className="text-xs text-muted-foreground mb-1">Time Horizon</p>
            <Badge variant="outline">
              {commodity.investmentHorizon}
            </Badge>
          </div>
        </div>
        
        <div className="space-y-2">
          <ScoreIndicator 
            score={commodity.overallScore} 
            label="Overall Score" 
            size="sm"
          />
          <RiskBenefitIndicator
            ratio={commodity.riskBenefitRatio}
            size="sm"
          />
        </div>
      </CardContent>
    </Card>
  );
}
