
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Stock } from '@/utils/stockApi';

interface TopMoversProps {
  stocks: Stock[];
  onStockSelect: (symbol: string) => void;
  isLoading?: boolean;
  error?: string | null;
  maxItems?: number;
}

export function TopMovers({ 
  stocks, 
  onStockSelect, 
  isLoading = false, 
  error = null,
  maxItems = 5 
}: TopMoversProps) {
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Today's Top Movers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-destructive p-4">
            Error loading top movers: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Today's Top Movers</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="divide-y divide-border">
            {[...Array(maxItems)].map((_, index) => (
              <div key={index} className="p-3 flex items-center justify-between">
                <div className="animate-pulse bg-muted rounded h-4 w-16" />
                <div className="animate-pulse bg-muted rounded h-4 w-12" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const topMovers = stocks
    .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
    .slice(0, maxItems);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Today's Top Movers</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="divide-y divide-border">
          {topMovers.map(stock => (
            <div 
              key={stock.symbol}
              className="p-3 flex items-center justify-between hover:bg-muted/30 transition-colors cursor-pointer"
              onClick={() => onStockSelect(stock.symbol)}
            >
              <div>
                <p className="font-medium">{stock.symbol}</p>
              </div>
              <div className={`text-sm ${stock.changePercent >= 0 ? 'text-success' : 'text-danger'}`}>
                {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export type { TopMoversProps };
