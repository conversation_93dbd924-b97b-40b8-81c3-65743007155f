
import { Badge } from "@/components/ui/badge";
import type { Commodity } from "@/types/commodities";

interface CommodityHeaderProps {
  commodity: Commodity;
}

export function CommodityHeader({ commodity }: CommodityHeaderProps) {
  return (
    <div className="flex items-start justify-between">
      <div>
        <div className="flex items-center gap-2 text-2xl font-semibold leading-none tracking-tight">
          {commodity.name}
          <Badge variant="outline" className="font-normal">
            {commodity.symbol}
          </Badge>
        </div>
        <div className="text-base mt-1 text-muted-foreground">{commodity.category}</div>
      </div>
      <div className="text-right">
        <div className="text-2xl font-bold">
          ${commodity.currentPrice.toFixed(2)}
          <span className="text-sm font-normal text-muted-foreground ml-1">
            /{commodity.unit}
          </span>
        </div>
        <div className={`text-sm font-medium ${commodity.changePercent >= 0 ? "text-success" : "text-danger"}`}>
          {commodity.changePercent >= 0 ? "+" : ""}
          {commodity.change.toFixed(2)} ({commodity.changePercent >= 0 ? "+" : ""}
          {commodity.changePercent.toFixed(2)}%)
        </div>
      </div>
    </div>
  );
}
