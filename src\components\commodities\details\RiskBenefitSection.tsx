
import { Badge } from "@/components/ui/badge";
import { RiskBenefitIndicator } from "@/components/commodities/RiskBenefitIndicator";
import { ScoreIndicator } from "@/components/prospective/ScoreIndicator";
import { ShieldAlert, TrendingUp } from "lucide-react";
import type { Commodity } from "@/types/commodities";

interface RiskBenefitSectionProps {
  commodity: Commodity;
}

export function RiskBenefitSection({ commodity }: RiskBenefitSectionProps) {
  return (
    <div>
      <div className="mb-6">
        <h4 className="font-medium mb-2">Risk/Benefit Assessment</h4>
        <div className="space-y-3">
          <RiskBenefitIndicator ratio={commodity.riskBenefitRatio} size="lg" />
          
          <div className="space-y-2">
            <div className="bg-muted p-3 rounded-md">
              <div className="flex items-center gap-2 mb-1">
                <ShieldAlert className="h-4 w-4 text-muted-foreground" />
                <h5 className="text-sm font-medium">Risk Level</h5>
              </div>
              <Badge 
                variant={
                  commodity.riskLevel === "Low" ? "success" :
                  commodity.riskLevel === "Moderate" ? "warning" :
                  commodity.riskLevel === "High" ? "destructive" :
                  "destructive"
                }
                className={commodity.riskLevel === "Very High" ? "bg-red-800" : ""}
              >
                {commodity.riskLevel}
              </Badge>
              <p className="text-xs text-muted-foreground mt-1">
                Volatility: {commodity.volatility.toFixed(1)}%
              </p>
            </div>
            
            <div className="bg-muted p-3 rounded-md">
              <div className="flex items-center gap-2 mb-1">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <h5 className="text-sm font-medium">Investment Horizon</h5>
              </div>
              <Badge variant="outline">{commodity.investmentHorizon}</Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h4 className="font-medium mb-2">Performance Scores</h4>
        <div className="space-y-3">
          <ScoreIndicator score={commodity.overallScore} label="Overall Score" size="lg" />
          <ScoreIndicator score={commodity.technicalScore} label="Technical" />
          <ScoreIndicator score={commodity.fundamentalScore} label="Fundamental" />
          <ScoreIndicator score={commodity.sentimentScore} label="Sentiment" />
        </div>
      </div>
    </div>
  );
}
