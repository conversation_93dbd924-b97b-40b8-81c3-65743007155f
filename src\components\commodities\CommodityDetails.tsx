
import { Commodity } from "@/types/commodities";
import {
  Card,
  CardContent,
  CardHeader,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import { CommodityHeader } from "./details/CommodityHeader";
import { RiskBenefitSection } from "./details/RiskBenefitSection";
import { MetricsSection } from "./details/MetricsSection";

interface CommodityDetailsProps {
  commodity: Commodity | null;
}

export function CommodityDetails({ commodity }: CommodityDetailsProps) {
  if (!commodity) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Commodity Details</CardTitle>
          <CardDescription>Select a commodity to view details</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CommodityHeader commodity={commodity} />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <RiskBenefitSection commodity={commodity} />
          <MetricsSection commodity={commodity} />
        </div>
      </CardContent>
    </Card>
  );
}
