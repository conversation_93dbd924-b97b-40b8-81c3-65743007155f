
import { Commodity, CommodityCategory, RiskLevel, InvestmentHorizon } from "@/types/commodities";

// Helper function to generate a random number within a range
const randomInRange = (min: number, max: number): number => {
  return +(Math.random() * (max - min) + min).toFixed(2);
};

// Helper function to get risk level based on volatility
const getRiskLevel = (volatility: number): RiskLevel => {
  if (volatility < 15) return "Low";
  if (volatility < 25) return "Moderate";
  if (volatility < 35) return "High";
  return "Very High";
};

// Helper function to calculate risk/benefit ratio
const calculateRiskBenefitRatio = (overallScore: number, volatility: number): number => {
  return +(overallScore / volatility).toFixed(2);
};

// Generate sample commodity data
export const commodities: Commodity[] = [
  // Energy
  {
    id: "crude-oil",
    name: "Crude Oil (WTI)",
    symbol: "CL",
    category: "Energy",
    currentPrice: 76.32,
    changePercent: -0.84,
    change: -0.65,
    currency: "USD",
    unit: "barrel",
    volume: 278500,
    openInterest: 323460,
    yearToDateReturn: 7.42,
    yearHigh: 82.64,
    yearLow: 65.17,
    volatility: 28.5,
    technicalScore: 62,
    fundamentalScore: 73,
    sentimentScore: 58,
    overallScore: 65,
    riskLevel: "High",
    riskBenefitRatio: 2.28,
    correlationWithSP500: 0.45,
    inflationHedgeScore: 78,
    recessionResistanceScore: 40,
    sustainabilityScore: 24,
    investmentHorizon: "Medium-term"
  },
  {
    id: "natural-gas",
    name: "Natural Gas",
    symbol: "NG",
    category: "Energy",
    currentPrice: 2.92,
    changePercent: 1.35,
    change: 0.04,
    currency: "USD",
    unit: "MMBtu",
    volume: 186200,
    openInterest: 193750,
    yearToDateReturn: -15.21,
    yearHigh: 4.63,
    yearLow: 1.97,
    volatility: 42.3,
    technicalScore: 45,
    fundamentalScore: 60,
    sentimentScore: 51,
    overallScore: 52,
    riskLevel: "Very High",
    riskBenefitRatio: 1.23,
    correlationWithSP500: 0.22,
    inflationHedgeScore: 65,
    recessionResistanceScore: 35,
    sustainabilityScore: 48,
    investmentHorizon: "Medium-term"
  },
  // Precious Metals
  {
    id: "gold",
    name: "Gold",
    symbol: "GC",
    category: "Precious Metals",
    currentPrice: 2324.10,
    changePercent: 0.42,
    change: 9.70,
    currency: "USD",
    unit: "troy ounce",
    volume: 197300,
    openInterest: 456780,
    yearToDateReturn: 12.45,
    yearHigh: 2427.50,
    yearLow: 1805.60,
    volatility: 14.2,
    technicalScore: 78,
    fundamentalScore: 83,
    sentimentScore: 79,
    overallScore: 80,
    riskLevel: "Low",
    riskBenefitRatio: 5.63,
    correlationWithSP500: -0.15,
    inflationHedgeScore: 92,
    recessionResistanceScore: 88,
    sustainabilityScore: 62,
    investmentHorizon: "Long-term"
  },
  {
    id: "silver",
    name: "Silver",
    symbol: "SI",
    category: "Precious Metals",
    currentPrice: 29.45,
    changePercent: 0.58,
    change: 0.17,
    currency: "USD",
    unit: "troy ounce",
    volume: 112500,
    openInterest: 178340,
    yearToDateReturn: 21.85,
    yearHigh: 31.82,
    yearLow: 22.44,
    volatility: 22.7,
    technicalScore: 76,
    fundamentalScore: 74,
    sentimentScore: 70,
    overallScore: 73,
    riskLevel: "Moderate",
    riskBenefitRatio: 3.22,
    correlationWithSP500: 0.18,
    inflationHedgeScore: 86,
    recessionResistanceScore: 72,
    sustainabilityScore: 58,
    investmentHorizon: "Long-term"
  },
  // Industrial Metals
  {
    id: "copper",
    name: "Copper",
    symbol: "HG",
    category: "Industrial Metals",
    currentPrice: 4.23,
    changePercent: -0.35,
    change: -0.015,
    currency: "USD",
    unit: "pound",
    volume: 96400,
    openInterest: 142560,
    yearToDateReturn: 9.84,
    yearHigh: 4.78,
    yearLow: 3.62,
    volatility: 18.6,
    technicalScore: 68,
    fundamentalScore: 77,
    sentimentScore: 65,
    overallScore: 72,
    riskLevel: "Moderate",
    riskBenefitRatio: 3.87,
    correlationWithSP500: 0.52,
    inflationHedgeScore: 74,
    recessionResistanceScore: 45,
    sustainabilityScore: 65,
    investmentHorizon: "Medium-term"
  },
  // Agriculture
  {
    id: "corn",
    name: "Corn",
    symbol: "ZC",
    category: "Agriculture",
    currentPrice: 445.25,
    changePercent: 0.12,
    change: 0.53,
    currency: "USD",
    unit: "bushel",
    volume: 158700,
    openInterest: 204350,
    yearToDateReturn: -8.32,
    yearHigh: 534.75,
    yearLow: 405.50,
    volatility: 16.4,
    technicalScore: 55,
    fundamentalScore: 62,
    sentimentScore: 58,
    overallScore: 58,
    riskLevel: "Moderate",
    riskBenefitRatio: 3.54,
    correlationWithSP500: 0.18,
    inflationHedgeScore: 68,
    recessionResistanceScore: 65,
    sustainabilityScore: 72,
    investmentHorizon: "Medium-term"
  },
  {
    id: "soybeans",
    name: "Soybeans",
    symbol: "ZS",
    category: "Agriculture",
    currentPrice: 1182.50,
    changePercent: -0.28,
    change: -3.25,
    currency: "USD",
    unit: "bushel",
    volume: 129600,
    openInterest: 165420,
    yearToDateReturn: -4.75,
    yearHigh: 1385.25,
    yearLow: 1145.75,
    volatility: 15.8,
    technicalScore: 53,
    fundamentalScore: 60,
    sentimentScore: 56,
    overallScore: 56,
    riskLevel: "Moderate",
    riskBenefitRatio: 3.54,
    correlationWithSP500: 0.20,
    inflationHedgeScore: 65,
    recessionResistanceScore: 62,
    sustainabilityScore: 75,
    investmentHorizon: "Medium-term"
  },
  // Add more commodities...
];

export const commodityCategories: CommodityCategory[] = [
  "Energy", 
  "Precious Metals", 
  "Industrial Metals", 
  "Agriculture", 
  "Livestock", 
  "Softs"
];

export const riskLevels: RiskLevel[] = ["Low", "Moderate", "High", "Very High"];

export const investmentHorizons: InvestmentHorizon[] = ["Short-term", "Medium-term", "Long-term"];

// Generate an extended list with more commodities for each category
export const generateExtendedCommoditiesList = (): Commodity[] => {
  const extendedList: Commodity[] = [...commodities];

  // These are sample commodities to add to our list
  const additionalCommodities = [
    // Energy
    { name: "Brent Crude Oil", symbol: "BZ", category: "Energy", unit: "barrel" },
    { name: "Heating Oil", symbol: "HO", category: "Energy", unit: "gallon" },
    { name: "Gasoline RBOB", symbol: "RB", category: "Energy", unit: "gallon" },
    { name: "Ethanol", symbol: "EH", category: "Energy", unit: "gallon" },
    
    // Precious Metals
    { name: "Platinum", symbol: "PL", category: "Precious Metals", unit: "troy ounce" },
    { name: "Palladium", symbol: "PA", category: "Precious Metals", unit: "troy ounce" },
    { name: "Aluminum", symbol: "AL", category: "Industrial Metals", unit: "ton" },
    
    // Industrial Metals
    { name: "Zinc", symbol: "ZN", category: "Industrial Metals", unit: "pound" },
    { name: "Lead", symbol: "LD", category: "Industrial Metals", unit: "pound" },
    { name: "Nickel", symbol: "NI", category: "Industrial Metals", unit: "ton" },
    { name: "Tin", symbol: "SN", category: "Industrial Metals", unit: "pound" },
    
    // Agriculture
    { name: "Wheat", symbol: "ZW", category: "Agriculture", unit: "bushel" },
    { name: "Rice", symbol: "ZR", category: "Agriculture", unit: "hundredweight" },
    { name: "Oats", symbol: "ZO", category: "Agriculture", unit: "bushel" },
    { name: "Canola", symbol: "RS", category: "Agriculture", unit: "ton" },
    
    // Livestock
    { name: "Live Cattle", symbol: "LE", category: "Livestock", unit: "pound" },
    { name: "Feeder Cattle", symbol: "GF", category: "Livestock", unit: "pound" },
    { name: "Lean Hogs", symbol: "HE", category: "Livestock", unit: "pound" },
    
    // Softs
    { name: "Cotton", symbol: "CT", category: "Softs", unit: "pound" },
    { name: "Coffee", symbol: "KC", category: "Softs", unit: "pound" },
    { name: "Sugar", symbol: "SB", category: "Softs", unit: "pound" },
    { name: "Cocoa", symbol: "CC", category: "Softs", unit: "ton" },
    { name: "Orange Juice", symbol: "OJ", category: "Softs", unit: "pound" }
  ];
  
  // Generate data for each additional commodity
  additionalCommodities.forEach((commodity, index) => {
    const id = commodity.name.toLowerCase().replace(/\s+/g, '-');
    const currentPrice = randomInRange(10, 2000);
    const changePercent = randomInRange(-2, 2);
    const volatility = randomInRange(10, 40);
    const technicalScore = randomInRange(40, 90);
    const fundamentalScore = randomInRange(40, 90);
    const sentimentScore = randomInRange(40, 90);
    const overallScore = Math.round((technicalScore + fundamentalScore + sentimentScore) / 3);
    const riskLevel = getRiskLevel(volatility);
    const riskBenefitRatio = calculateRiskBenefitRatio(overallScore, volatility);
    
    extendedList.push({
      id,
      name: commodity.name,
      symbol: commodity.symbol,
      category: commodity.category as CommodityCategory,
      currentPrice,
      changePercent,
      change: +(currentPrice * changePercent / 100).toFixed(2),
      currency: "USD",
      unit: commodity.unit,
      volume: Math.round(randomInRange(50000, 300000)),
      openInterest: Math.round(randomInRange(80000, 400000)),
      yearToDateReturn: randomInRange(-20, 25),
      yearHigh: currentPrice * (1 + randomInRange(0.1, 0.3)),
      yearLow: currentPrice * (1 - randomInRange(0.1, 0.3)),
      volatility,
      technicalScore,
      fundamentalScore,
      sentimentScore,
      overallScore,
      riskLevel,
      riskBenefitRatio,
      correlationWithSP500: randomInRange(-0.5, 0.7),
      inflationHedgeScore: randomInRange(50, 95),
      recessionResistanceScore: randomInRange(30, 90),
      sustainabilityScore: commodity.category === "Energy" ? randomInRange(20, 60) : randomInRange(50, 85),
      investmentHorizon: ["Short-term", "Medium-term", "Long-term"][Math.floor(Math.random() * 3)] as InvestmentHorizon
    });
  });
  
  return extendedList;
};

// Export the extended list
export const allCommodities = generateExtendedCommoditiesList();
