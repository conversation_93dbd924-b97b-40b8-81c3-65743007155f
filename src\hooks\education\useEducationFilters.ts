
import { useMemo } from 'react';
import { Tutorial, EducationModule } from '@/types/education';

export function useEducationFilters(
  tutorials: Tutorial[],
  modules: EducationModule[],
  activeFilter: string
) {
  const filteredTutorials = useMemo(() => {
    if (activeFilter === 'all') return tutorials;
    return tutorials.filter(tutorial => tutorial.difficulty.toLowerCase() === activeFilter);
  }, [tutorials, activeFilter]);

  const filteredModules = useMemo(() => {
    if (activeFilter === 'all') return modules;
    return modules.filter(module => module.category.toLowerCase().includes(activeFilter));
  }, [modules, activeFilter]);

  return {
    filteredTutorials,
    filteredModules
  };
}
