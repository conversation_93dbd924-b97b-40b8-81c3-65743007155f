
import { formatLargeCurrency } from '@/utils/formatters';
import { MetricCard } from './MetricCard';
import type { BalanceSheetTabProps } from './types';

export function BalanceSheetTab({ balanceSheetData }: BalanceSheetTabProps) {
  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-2 px-4">Period</th>
              <th className="text-right py-2 px-4">Total Assets</th>
              <th className="text-right py-2 px-4">Total Liabilities</th>
              <th className="text-right py-2 px-4">Total Equity</th>
              <th className="text-right py-2 px-4">Cash & Equivalents</th>
              <th className="text-right py-2 px-4">Debt/Equity</th>
            </tr>
          </thead>
          <tbody>
            {balanceSheetData.map((data) => (
              <tr key={data.period} className="border-b">
                <td className="py-3 px-4">{data.period}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.totalAssets)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.totalLiabilities)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.totalEquity)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.cashAndEquivalents)}</td>
                <td className="text-right py-3 px-4">{data.debtToEquityRatio.toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <MetricCard
          title="Current Ratio"
          value="0.82"
          trend="Below Target"
          status="text-danger"
        />
        <MetricCard
          title="Return on Assets"
          value="27.5%"
          trend="Industry Leading"
          status="text-success"
        />
        <MetricCard
          title="Return on Equity"
          value="156.2%"
          trend="High Leverage"
          status="text-muted-foreground"
        />
      </div>
    </div>
  );
}
