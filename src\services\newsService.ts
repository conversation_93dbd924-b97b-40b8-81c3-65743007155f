import { NewsItem } from '@/utils/stockApi';

export interface NewsServiceOptions {
  category?: string;
  limit?: number;
  sortBy?: 'date' | 'relevance';
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export class NewsService {
  private static instance: NewsService;

  public static getInstance(): NewsService {
    if (!NewsService.instance) {
      NewsService.instance = new NewsService();
    }
    return NewsService.instance;
  }

  /**
   * Filter and sort news items
   */
  getFilteredNews(news: NewsItem[], options: NewsServiceOptions = {}): NewsItem[] {
    try {
      const { category, limit = 10, sortBy = 'date', sentiment } = options;

      let filteredNews = [...news];

      // Filter by category
      if (category) {
        filteredNews = filteredNews.filter(item => 
          item.category?.toLowerCase() === category.toLowerCase()
        );
      }

      // Filter by sentiment
      if (sentiment) {
        filteredNews = filteredNews.filter(item => 
          item.sentiment?.toLowerCase() === sentiment.toLowerCase()
        );
      }

      // Sort news
      if (sortBy === 'date') {
        filteredNews.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      } else if (sortBy === 'relevance') {
        // Sort by relevance (could be based on engagement, views, etc.)
        // For now, we'll use a simple heuristic
        filteredNews.sort((a, b) => {
          const scoreA = this.calculateRelevanceScore(a);
          const scoreB = this.calculateRelevanceScore(b);
          return scoreB - scoreA;
        });
      }

      // Limit results
      return filteredNews.slice(0, limit);
    } catch (error) {
      console.error('Error in NewsService.getFilteredNews:', error);
      return [];
    }
  }

  /**
   * Get news by stock symbol
   */
  getStockNews(news: NewsItem[], symbol: string, limit: number = 5): NewsItem[] {
    try {
      const stockNews = news.filter(item => 
        item.title.toLowerCase().includes(symbol.toLowerCase()) ||
        item.summary.toLowerCase().includes(symbol.toLowerCase())
      );

      return stockNews
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error in NewsService.getStockNews:', error);
      return [];
    }
  }

  /**
   * Get trending news (most recent with positive sentiment)
   */
  getTrendingNews(news: NewsItem[], limit: number = 5): NewsItem[] {
    try {
      return news
        .filter(item => item.sentiment === 'positive')
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error in NewsService.getTrendingNews:', error);
      return [];
    }
  }

  /**
   * Search news by keyword
   */
  searchNews(news: NewsItem[], keyword: string): NewsItem[] {
    try {
      if (!keyword.trim()) {
        return news;
      }

      const searchTerm = keyword.toLowerCase();
      return news.filter(item =>
        item.title.toLowerCase().includes(searchTerm) ||
        item.summary.toLowerCase().includes(searchTerm) ||
        item.category?.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error in NewsService.searchNews:', error);
      return [];
    }
  }

  /**
   * Get news categories
   */
  getNewsCategories(news: NewsItem[]): string[] {
    try {
      const categories = new Set<string>();
      news.forEach(item => {
        if (item.category) {
          categories.add(item.category);
        }
      });
      return Array.from(categories).sort();
    } catch (error) {
      console.error('Error in NewsService.getNewsCategories:', error);
      return [];
    }
  }

  /**
   * Get news sentiment distribution
   */
  getSentimentDistribution(news: NewsItem[]) {
    try {
      const distribution = {
        positive: 0,
        negative: 0,
        neutral: 0
      };

      news.forEach(item => {
        if (item.sentiment) {
          distribution[item.sentiment as keyof typeof distribution]++;
        }
      });

      const total = news.length;
      return {
        positive: total > 0 ? (distribution.positive / total) * 100 : 0,
        negative: total > 0 ? (distribution.negative / total) * 100 : 0,
        neutral: total > 0 ? (distribution.neutral / total) * 100 : 0,
        total
      };
    } catch (error) {
      console.error('Error in NewsService.getSentimentDistribution:', error);
      return { positive: 0, negative: 0, neutral: 0, total: 0 };
    }
  }

  /**
   * Calculate relevance score for news item
   */
  private calculateRelevanceScore(newsItem: NewsItem): number {
    try {
      let score = 0;

      // Recent news gets higher score
      const daysSincePublished = Math.floor(
        (Date.now() - new Date(newsItem.date).getTime()) / (1000 * 60 * 60 * 24)
      );
      score += Math.max(0, 10 - daysSincePublished);

      // Positive sentiment gets bonus
      if (newsItem.sentiment === 'positive') {
        score += 5;
      } else if (newsItem.sentiment === 'negative') {
        score += 2; // Negative news can still be relevant
      }

      // Longer summaries might be more detailed
      if (newsItem.summary.length > 100) {
        score += 2;
      }

      return score;
    } catch (error) {
      console.error('Error calculating relevance score:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const newsService = NewsService.getInstance();
