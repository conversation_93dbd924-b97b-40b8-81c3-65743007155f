
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Stock, formatCurrency, formatPercentage, formatLargeCurrency } from '@/utils/stockApi';

interface StockDetailsProps {
  stock: Stock;
}

export function StockDetails({ stock }: StockDetailsProps) {
  const isPositive = stock.change >= 0;
  
  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-2xl">{stock.symbol}</CardTitle>
            <p className="text-muted-foreground">{stock.name}</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{formatCurrency(stock.price)}</div>
            <div className={`text-base ${isPositive ? 'text-success' : 'text-danger'}`}>
              {formatCurrency(stock.change)} ({formatPercentage(stock.changePercent)})
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Market Cap</p>
            <p className="text-base font-medium">{formatLargeCurrency(stock.marketCap)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">P/E Ratio</p>
            <p className="text-base font-medium">{stock.pe.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Dividend Yield</p>
            <p className="text-base font-medium">{(stock.dividend / stock.price * 100).toFixed(2)}%</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Beta</p>
            <p className="text-base font-medium">{stock.beta.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">EPS (TTM)</p>
            <p className="text-base font-medium">{formatCurrency(stock.eps)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">52-Week High</p>
            <p className="text-base font-medium">{formatCurrency(stock.high52)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">52-Week Low</p>
            <p className="text-base font-medium">{formatCurrency(stock.low52)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Volume</p>
            <p className="text-base font-medium">{stock.volume.toLocaleString()}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
