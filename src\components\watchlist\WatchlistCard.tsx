
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, Trash2, Plus } from 'lucide-react';
import { Stock, formatCurrency, formatPercentage } from '@/utils/stockApi';
import { cn } from '@/lib/utils';

interface WatchlistCardProps {
  title: string;
  stocks: Stock[];
  onAddClick?: () => void;
  onRemoveStock?: (symbol: string) => void;
  onSelectStock?: (symbol: string) => void;
  className?: string;
}

export function WatchlistCard({ 
  title, 
  stocks, 
  onAddClick,
  onRemoveStock,
  onSelectStock,
  className 
}: WatchlistCardProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <div className="flex items-center">
          <Star className="h-5 w-5 mr-2 text-warning" />
          <CardTitle>{title}</CardTitle>
        </div>
        
        {onAddClick && (
          <Button variant="ghost" size="sm" onClick={onAddClick}>
            <Plus className="h-4 w-4 mr-2" />
            Add Stock
          </Button>
        )}
      </CardHeader>
      <CardContent className="p-0">
        <div className="divide-y divide-border">
          {stocks.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              <p>No stocks in your watchlist.</p>
              <p className="text-sm mt-2">Click "Add Stock" to get started.</p>
            </div>
          ) : (
            stocks.map((stock) => (
              <div 
                key={stock.symbol} 
                className="p-3 flex items-center justify-between hover:bg-muted/30 transition-colors"
              >
                <div 
                  className="flex-1 cursor-pointer"
                  onClick={() => onSelectStock && onSelectStock(stock.symbol)}
                >
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">{stock.symbol}</p>
                      <p className="text-xs text-muted-foreground">{stock.name}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(stock.price)}</p>
                      <p className={cn(
                        "text-xs",
                        stock.changePercent >= 0 ? "text-success" : "text-danger"
                      )}>
                        {formatPercentage(stock.changePercent)}
                      </p>
                    </div>
                  </div>
                </div>
                
                {onRemoveStock && (
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-7 w-7 ml-2"
                    onClick={() => onRemoveStock(stock.symbol)}
                  >
                    <Trash2 className="h-3.5 w-3.5 text-muted-foreground" />
                  </Button>
                )}
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
