
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Shield, AlertTriangle, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RiskGuideCardProps {
  id: string;
  title: string;
  description: string;
  riskLevel: 'Low' | 'Medium' | 'High';
  keyPoints: string[];
  readTime: string;
  onRead: (id: string) => void;
  className?: string;
}

export const RiskGuideCard = React.memo(function RiskGuideCard({
  id,
  title,
  description,
  riskLevel,
  keyPoints,
  readTime,
  onRead,
  className
}: RiskGuideCardProps) {
  const riskColors = {
    Low: { bg: 'bg-green-100 text-green-800', icon: Shield },
    Medium: { bg: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
    High: { bg: 'bg-red-100 text-red-800', icon: TrendingUp }
  };

  const RiskIcon = riskColors[riskLevel].icon;

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <RiskIcon className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <Badge className={riskColors[riskLevel].bg} variant="secondary">
            {riskLevel} Risk
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-2">Key Points:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            {keyPoints.map((point, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0" />
                {point}
              </li>
            ))}
          </ul>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">{readTime} read</span>
          <Button onClick={() => onRead(id)} variant="outline" size="sm">
            Read Guide
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});
