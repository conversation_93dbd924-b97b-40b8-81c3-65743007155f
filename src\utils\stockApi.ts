
/**
 * MAIN API ENTRY POINT
 * This file serves as the single source of truth for all stock-related API functions.
 * All components should import from this file to ensure consistency.
 */

// Re-export all types from the centralized types directory
export * from '@/types/stock';

// Re-export utility functions
export * from './stockData';
export * from './financialData';
export * from './formatters';

// Import and re-export the main data and hooks from stocksApi
export {
  // Data exports
  mockStocks,
  mockNews,
  mockIndices,
  mockCurrencies,
  mockCryptos,

  // Hook exports
  useStockData,
  useMarketIndices,
  useCurrencyPairs,
  useCryptoData,

  // Utility function exports
  generatePriceHistory,
  getStockBySymbol
} from './stocksApi';

// Re-export types that might be needed
export type { Cryptocurrency, MarketIndex, CurrencyPair } from './stocksApi';

// Re-export specific functions that might be imported directly
export { getPredictions } from './stockData';
