
/**
 * MAIN API ENTRY POINT
 * This file serves as the single source of truth for all stock-related API functions.
 * All components should import from this file to ensure consistency.
 */

// Re-export all types from the centralized types directory
export * from '@/types/stock';

// Import and re-export the main data and hooks from stocksApi (PRIMARY SOURCE)
export {
  // Data exports
  mockStocks,
  mockNews,
  mockIndices,
  mockCurrencies,
  mockCryptos,

  // Hook exports - USE ONLY FROM stocksApi.ts
  useStockData,
  useMarketIndices,
  useCurrencyPairs,
  useCryptoData,

  // Utility function exports
  generatePriceHistory,
  getStockBySymbol,
  formatNumber,
  formatPercentage,
  formatDate
} from './stocksApi';

// Re-export types that might be needed
export type { Cryptocurrency, MarketIndex, CurrencyPair } from './stocksApi';

// Re-export specific functions from other files (NON-CONFLICTING)
export { getPredictions } from './stockData';
export * from './financialData';
export * from './formatters';
