
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 14%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 14%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 52%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217 33% 20%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 20%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 72% 46%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --danger: 0 84% 60%;
    --danger-foreground: 0 0% 100%;

    --border: 217 33% 20%;
    --input: 217 33% 20%;
    --ring: 222 47% 24%;

    --radius: 0.5rem;
    
    --sidebar: 222 47% 9%;
    --sidebar-foreground: 210 40% 98%;
  }

  /* Text size variations */
  html[data-text-size="small"] {
    font-size: 14px;
  }

  html[data-text-size="medium"] {
    font-size: 16px;
  }

  html[data-text-size="large"] {
    font-size: 18px;
  }

  html[data-text-size="extra-large"] {
    font-size: 20px;
  }

  /* High contrast mode */
  .high-contrast {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 5%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 100%;
    --primary: 210 100% 70%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 85%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;
    --border: 0 0% 30%;
    --input: 0 0% 15%;
    --ring: 210 100% 70%;
  }

  .high-contrast * {
    border-color: hsl(var(--border)) !important;
  }

  /* Screen reader mode optimizations */
  .screen-reader-mode {
    line-height: 1.6;
  }

  .screen-reader-mode * {
    transition: none !important;
    animation: none !important;
  }

  .screen-reader-mode button,
  .screen-reader-mode a,
  .screen-reader-mode input,
  .screen-reader-mode select,
  .screen-reader-mode textarea {
    outline: 2px solid hsl(var(--primary)) !important;
    outline-offset: 2px !important;
  }

  .screen-reader-mode button:focus,
  .screen-reader-mode a:focus,
  .screen-reader-mode input:focus,
  .screen-reader-mode select:focus,
  .screen-reader-mode textarea:focus {
    outline: 3px solid hsl(var(--primary)) !important;
    outline-offset: 3px !important;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    @apply h-full overflow-x-hidden antialiased;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Chart tooltips */
  .recharts-tooltip-wrapper {
    @apply !shadow-lg;
  }

  .recharts-default-tooltip {
    @apply !bg-card/90 !backdrop-blur !border-border !border !shadow-lg !px-3 !py-2 !rounded-md;
  }

  /* Stock trend colors */
  .trend-positive {
    @apply text-success;
  }
  
  .trend-negative {
    @apply text-danger;
  }
}
