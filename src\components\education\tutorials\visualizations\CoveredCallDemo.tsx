import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Area, AreaChart } from 'recharts';
import { Shield, DollarSign, TrendingUp, AlertCircle } from 'lucide-react';

interface CoveredCallData {
  stockPrice: number;
  stockProfit: number;
  callProfit: number;
  totalProfit: number;
  callValue: number;
}

export const CoveredCallDemo = React.memo(function CoveredCallDemo() {
  const [stockPrice, setStockPrice] = useState([100]);
  const [strikePrice, setStrikePrice] = useState([105]);
  const [premium, setPremium] = useState([3]);
  const [showScenarios, setShowScenarios] = useState(false);

  const generateCoveredCallData = (): CoveredCallData[] => {
    const data: CoveredCallData[] = [];
    const currentStock = stockPrice[0];
    const strike = strikePrice[0];
    const premiumReceived = premium[0];

    for (let price = 70; price <= 140; price += 2) {
      // Stock profit/loss from current price
      const stockProfit = price - currentStock;
      
      // Call option profit (we sold the call, so we keep premium if OTM)
      const callIntrinsic = Math.max(0, price - strike);
      const callProfit = premiumReceived - callIntrinsic;
      
      // Total covered call profit
      const totalProfit = stockProfit + callProfit;

      data.push({
        stockPrice: price,
        stockProfit,
        callProfit,
        totalProfit,
        callValue: callIntrinsic
      });
    }

    return data;
  };

  const data = generateCoveredCallData();

  const getScenarios = () => {
    const currentStock = stockPrice[0];
    const strike = strikePrice[0];
    const premiumReceived = premium[0];

    const scenarios = [
      {
        name: 'Stock Below Strike',
        stockPrice: strike - 5,
        description: 'Option expires worthless',
        icon: <Shield className="h-4 w-4 text-green-500" />
      },
      {
        name: 'Stock At Strike',
        stockPrice: strike,
        description: 'Maximum profit achieved',
        icon: <DollarSign className="h-4 w-4 text-blue-500" />
      },
      {
        name: 'Stock Above Strike',
        stockPrice: strike + 10,
        description: 'Stock called away',
        icon: <TrendingUp className="h-4 w-4 text-orange-500" />
      }
    ];

    return scenarios.map(scenario => {
      const stockProfit = scenario.stockPrice - currentStock;
      const callIntrinsic = Math.max(0, scenario.stockPrice - strike);
      const callProfit = premiumReceived - callIntrinsic;
      const totalProfit = stockProfit + callProfit;

      return {
        ...scenario,
        stockProfit,
        callProfit,
        totalProfit
      };
    });
  };

  const maxProfit = () => {
    const currentStock = stockPrice[0];
    const strike = strikePrice[0];
    const premiumReceived = premium[0];
    return (strike - currentStock) + premiumReceived;
  };

  const breakeven = () => {
    return stockPrice[0] - premium[0];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-500" />
          Covered Call Strategy Simulator
        </CardTitle>
        <CardDescription>
          Analyze the risk/reward profile of selling call options against your stock position
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Current Stock Price: ${stockPrice[0]}</label>
            <Slider
              value={stockPrice}
              onValueChange={setStockPrice}
              max={120}
              min={80}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Call Strike Price: ${strikePrice[0]}</label>
            <Slider
              value={strikePrice}
              onValueChange={setStrikePrice}
              max={130}
              min={stockPrice[0]}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Premium Received: ${premium[0]}</label>
            <Slider
              value={premium}
              onValueChange={setPremium}
              max={8}
              min={1}
              step={0.25}
              className="w-full"
            />
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Max Profit</div>
            <div className="text-lg font-bold text-green-600">${maxProfit().toFixed(2)}</div>
          </div>
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Premium Collected</div>
            <div className="text-lg font-bold text-blue-600">${premium[0]}</div>
          </div>
          <div className="text-center p-3 bg-orange-50 dark:bg-orange-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Breakeven</div>
            <div className="text-lg font-bold text-orange-600">${breakeven().toFixed(2)}</div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Upside Cap</div>
            <div className="text-lg font-bold text-purple-600">${strikePrice[0]}</div>
          </div>
        </div>

        {/* Toggle Scenarios */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => setShowScenarios(!showScenarios)}
          >
            {showScenarios ? 'Show Chart' : 'Show Scenarios'}
          </Button>
        </div>

        {showScenarios ? (
          /* Scenario Analysis */
          <div className="space-y-4">
            <h4 className="font-medium">Scenario Analysis</h4>
            <div className="grid gap-4">
              {getScenarios().map((scenario, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {scenario.icon}
                      <div>
                        <div className="font-medium">{scenario.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Stock at ${scenario.stockPrice} - {scenario.description}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">
                        ${scenario.totalProfit.toFixed(2)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Stock: ${scenario.stockProfit.toFixed(2)} | Call: ${scenario.callProfit.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          /* Payoff Chart */
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="stockPrice" 
                  tickFormatter={(value) => `$${value}`}
                />
                <YAxis tickFormatter={(value) => `$${value}`} />
                <Tooltip 
                  formatter={(value: number, name: string) => {
                    const labels = {
                      stockProfit: 'Stock P&L',
                      callProfit: 'Call P&L',
                      totalProfit: 'Total P&L'
                    };
                    return [`$${value.toFixed(2)}`, labels[name as keyof typeof labels] || name];
                  }}
                  labelFormatter={(value) => `Stock Price: $${value}`}
                />
                <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
                <ReferenceLine x={stockPrice[0]} stroke="#888" strokeDasharray="2 2" label="Current" />
                <ReferenceLine x={strikePrice[0]} stroke="#f59e0b" strokeDasharray="4 4" label="Strike" />
                <ReferenceLine x={breakeven()} stroke="#ef4444" strokeDasharray="4 4" label="Breakeven" />
                
                <Area
                  type="monotone"
                  dataKey="totalProfit"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.1}
                  strokeWidth={3}
                />
                <Line 
                  type="monotone" 
                  dataKey="stockProfit" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
                <Line 
                  type="monotone" 
                  dataKey="callProfit" 
                  stroke="#ef4444" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Strategy Summary */}
        <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Covered Call Summary</h4>
              <div className="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                <p>• You own {stockPrice[0] >= 100 ? 100 : Math.floor(stockPrice[0])} shares at ${stockPrice[0]} per share</p>
                <p>• You sold 1 call option with ${strikePrice[0]} strike for ${premium[0]} premium</p>
                <p>• Maximum profit: ${maxProfit().toFixed(2)} (if stock closes at or above ${strikePrice[0]})</p>
                <p>• Breakeven point: ${breakeven().toFixed(2)} (current price minus premium)</p>
                <p>• Risk: Stock could decline below breakeven, but premium provides some protection</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
