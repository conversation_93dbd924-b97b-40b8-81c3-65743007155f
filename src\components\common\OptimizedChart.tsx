
import React, { memo, useMemo, useCallback } from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { usePerformance } from '@/hooks/usePerformance';
import { cn } from '@/lib/utils';

interface ChartDataPoint {
  date: string;
  value: number;
  volume?: number;
}

interface OptimizedChartProps {
  data: ChartDataPoint[];
  height?: number;
  className?: string;
  color?: string;
  enableAnimation?: boolean;
  showTooltip?: boolean;
  showGrid?: boolean;
  accessibilityLabel?: string;
}

const OptimizedChartComponent = memo(function OptimizedChart({
  data,
  height = 300,
  className,
  color = "hsl(var(--primary))",
  enableAnimation = true,
  showTooltip = true,
  showGrid = true,
  accessibilityLabel = "Financial chart"
}: OptimizedChartProps) {
  const { getCache, setCache } = usePerformance<any[]>({ cacheExpiry: 10 * 60 * 1000 }); // 10 minutes

  const chartData = useMemo(() => {
    const cacheKey = `chart-${JSON.stringify(data.slice(0, 3))}-${data.length}`;
    const cached = getCache(cacheKey);
    
    if (cached && Array.isArray(cached)) {
      return cached;
    }

    const processedData = data.map((point, index) => ({
      ...point,
      index,
      displayDate: new Date(point.date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    }));

    setCache(cacheKey, processedData);
    return processedData;
  }, [data, getCache, setCache]);

  const formatTooltip = useCallback((value: number, name: string) => {
    if (name === 'value') {
      return [`$${value.toFixed(2)}`, 'Price'];
    }
    return [value, name];
  }, []);

  const formatYAxis = useCallback((value: number) => {
    return `$${value.toFixed(0)}`;
  }, []);

  return (
    <div 
      className={cn("w-full", className)} 
      style={{ height }}
      role="img"
      aria-label={accessibilityLabel}
    >
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={chartData}
          margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="chartGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={color} stopOpacity={0.3} />
              <stop offset="95%" stopColor={color} stopOpacity={0} />
            </linearGradient>
          </defs>
          
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              vertical={false} 
              stroke="hsl(var(--border))" 
            />
          )}
          
          <XAxis 
            dataKey="displayDate" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 10 }}
            tickMargin={10}
          />
          
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 10 }}
            tickMargin={10}
            tickFormatter={formatYAxis}
            width={60}
          />
          
          {showTooltip && (
            <Tooltip
              contentStyle={{
                background: "hsl(var(--card))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "var(--radius)",
                boxShadow: "0 2px 10px rgba(0, 0, 0, 0.1)",
              }}
              formatter={formatTooltip}
              labelFormatter={(label) => `Date: ${label}`}
            />
          )}
          
          <Area 
            type="monotone" 
            dataKey="value" 
            stroke={color} 
            fillOpacity={1}
            fill="url(#chartGradient)"
            strokeWidth={2}
            isAnimationActive={enableAnimation}
            animationDuration={enableAnimation ? 300 : 0}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
});

export { OptimizedChartComponent as OptimizedChart };
export type { OptimizedChartProps, ChartDataPoint };
