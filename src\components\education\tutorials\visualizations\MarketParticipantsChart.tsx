
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

const data = [
  { name: 'Institutional Investors', value: 70, color: '#3b82f6' },
  { name: 'Retail Investors', value: 20, color: '#10b981' },
  { name: 'Foreign Investors', value: 7, color: '#f59e0b' },
  { name: 'Others', value: 3, color: '#6b7280' }
];

export const MarketParticipantsChart = React.memo(function MarketParticipantsChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center">Market Participation Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value) => [`${value}%`, 'Market Share']}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-4 space-y-2">
          {data.map((item, index) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: item.color }}
                />
                <span>{item.name}</span>
              </div>
              <span className="font-mono">{item.value}%</span>
            </div>
          ))}
        </div>

        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Key Insight:</strong> Institutional investors control the majority of market activity, 
            which is why their decisions can significantly impact stock prices.
          </p>
        </div>
      </CardContent>
    </Card>
  );
});
