
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, Smartphone } from 'lucide-react';
import { usePWA } from '@/hooks/usePWA';
import { cn } from '@/lib/utils';

interface PWAInstallButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'lg';
}

export function PWAInstallButton({
  className,
  variant = 'outline',
  size = 'sm'
}: PWAInstallButtonProps) {
  const { isInstallable, isInstalled, installPWA } = usePWA();

  const handleInstall = async () => {
    const success = await installPWA();
    if (success) {
      console.log('PWA installed successfully');
    }
  };

  if (!isInstallable || isInstalled) {
    return null;
  }

  return (
    <Button
      onClick={handleInstall}
      variant={variant}
      size={size}
      className={cn("gap-2", className)}
      aria-label="Install app on device"
    >
      <Smartphone className="h-4 w-4" />
      Install App
    </Button>
  );
}

export type { PWAInstallButtonProps };
