
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScoreIndicator } from "./ScoreIndicator";
import { ProspectiveStock } from "@/types/prospectiveStocks";
import { formatCurrency } from "@/utils/formatters";
import { Globe, ChartBar, ChartLine } from "lucide-react";

interface ProspectiveStockDetailProps {
  stock: ProspectiveStock | null;
}

export function ProspectiveStockDetail({ stock }: ProspectiveStockDetailProps) {
  if (!stock) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Stock Details</CardTitle>
          <CardDescription>Select a stock to view details</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const dividendYield = (stock.dividend / stock.price * 100).toFixed(2);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {stock.symbol}
              <span className="text-sm font-normal text-muted-foreground">
                ({stock.market})
              </span>
            </CardTitle>
            <CardDescription className="text-base mt-1">{stock.name}</CardDescription>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">${stock.price.toFixed(2)}</div>
            <div className={`text-sm font-medium ${stock.change >= 0 ? "text-success" : "text-danger"}`}>
              {stock.change >= 0 ? "+" : ""}
              {stock.change.toFixed(2)} ({stock.change >= 0 ? "+" : ""}
              {stock.changePercent.toFixed(2)}%)
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <div className="mb-6">
              <h4 className="font-medium mb-2">Performance Scores</h4>
              <div className="space-y-3">
                <ScoreIndicator
                  score={stock.overallScore}
                  label="Overall Score"
                  size="lg"
                />
                <ScoreIndicator
                  score={stock.growthScore}
                  label="Growth"
                />
                <ScoreIndicator
                  score={stock.valueScore}
                  label="Value"
                />
                <ScoreIndicator
                  score={stock.momentumScore}
                  label="Momentum"
                />
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium mb-2">Analyst Rating</h4>
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <div
                    key={star}
                    className={`w-8 h-1.5 rounded-full ${
                      star <= Math.round(stock.analystRating)
                        ? "bg-primary"
                        : "bg-muted"
                    }`}
                  />
                ))}
                <span className="ml-2 font-medium">{stock.analystRating.toFixed(1)}/5</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <span>Region</span>
              </div>
              <span className="font-medium">{stock.region}</span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <span>Sector</span>
              <Badge variant="outline">{stock.sector}</Badge>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <span>Industry</span>
              <span className="font-medium">{stock.industry}</span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ChartBar className="h-4 w-4 text-muted-foreground" />
                <span>Market Cap</span>
              </div>
              <span className="font-medium">{formatCurrency(stock.marketCap)}</span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <span>P/E Ratio</span>
              <span className="font-medium">{stock.pe.toFixed(2)}</span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <span>EPS</span>
              <span className="font-medium">${stock.eps.toFixed(2)}</span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <span>Dividend Yield</span>
              <span className="font-medium">{dividendYield}%</span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ChartLine className="h-4 w-4 text-muted-foreground" />
                <span>52-Week Range</span>
              </div>
              <span className="font-medium">
                ${stock.fiftyTwoWeekLow.toFixed(2)} - ${stock.fiftyTwoWeekHigh.toFixed(2)}
              </span>
            </div>
            <Separator />

            <div className="flex items-center justify-between">
              <span>Beta</span>
              <span className="font-medium">{stock.beta.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
