import React, { useState, useMemo, memo } from 'react';
import { OptimizedChart } from '@/components/common/OptimizedChart';
import { generatePriceHistory } from '@/utils/stockApi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';
import { cn } from '@/lib/utils';

const timeRanges = [
  { label: '1D', days: 1, interval: 24 },
  { label: '1W', days: 7, interval: 7 },
  { label: '1M', days: 30, interval: 30 },
  { label: '3M', days: 90, interval: 30 },
  { label: '1Y', days: 365, interval: 52 },
  { label: 'All', days: 1825, interval: 104 },
];

interface StockChartProps {
  symbol: string;
  name: string;
  currentPrice: number;
  volatility?: number;
  className?: string;
}

const StockChartComponent = memo(function StockChart({ 
  symbol, 
  name,
  currentPrice,
  volatility = 2,
  className
}: StockChartProps) {
  const [selectedRange, setSelectedRange] = useState(timeRanges[2]); // Default to 1M
  const [focusedButtonIndex, setFocusedButtonIndex] = useState(2);
  
  const chartData = useMemo(() => {
    const prices = generatePriceHistory(selectedRange.days, currentPrice, volatility);
    const data = [];
    
    // Calculate dates going backward from today
    const now = new Date();
    const msPerDay = 24 * 60 * 60 * 1000;
    
    for (let i = 0; i < prices.length; i++) {
      const date = new Date(now.getTime() - (selectedRange.days - i) * msPerDay);
      data.push({
        date: date.toISOString(),
        value: prices[i]
      });
    }
    
    return data;
  }, [selectedRange, currentPrice, volatility]);

  const { startListening, stopListening } = useKeyboardNavigation({
    onArrowLeft: () => {
      setFocusedButtonIndex(prev => Math.max(0, prev - 1));
    },
    onArrowRight: () => {
      setFocusedButtonIndex(prev => Math.min(timeRanges.length - 1, prev + 1));
    },
    onEnter: () => {
      setSelectedRange(timeRanges[focusedButtonIndex]);
    }
  });
  
  return (
    <Card 
      className={cn("overflow-hidden h-full", className)}
      onFocus={startListening}
      onBlur={stopListening}
    >
      <CardHeader className="flex-row items-center justify-between pb-4">
        <div>
          <CardTitle className="leading-none" id={`chart-title-${symbol}`}>
            {symbol}
          </CardTitle>
          <p className="text-sm text-muted-foreground">{name}</p>
        </div>
        <div 
          className="flex gap-1"
          role="tablist"
          aria-label="Chart time range selection"
        >
          {timeRanges.map((range, index) => (
            <Button 
              key={range.label} 
              variant={selectedRange.label === range.label ? "default" : "outline"} 
              size="sm"
              onClick={() => {
                setSelectedRange(range);
                setFocusedButtonIndex(index);
              }}
              className="h-7 px-2 text-xs"
              role="tab"
              aria-selected={selectedRange.label === range.label}
              aria-controls={`chart-panel-${symbol}`}
              tabIndex={focusedButtonIndex === index ? 0 : -1}
            >
              {range.label}
            </Button>
          ))}
        </div>
      </CardHeader>
      <CardContent className="p-0 pb-4">
        <div 
          id={`chart-panel-${symbol}`}
          role="tabpanel"
          aria-labelledby={`chart-title-${symbol}`}
          className="h-[300px] w-full px-4"
        >
          <OptimizedChart
            data={chartData}
            height={300}
            accessibilityLabel={`${name} stock price chart for ${selectedRange.label} time period`}
            enableAnimation={true}
            showTooltip={true}
            showGrid={true}
          />
        </div>
      </CardContent>
    </Card>
  );
});

export { StockChartComponent as StockChart };
export type { StockChartProps };
