
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Area, AreaChart } from 'recharts';
import { Shield, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react';

interface ProtectivePutData {
  stockPrice: number;
  stockProfit: number;
  putProfit: number;
  totalProfit: number;
  putValue: number;
  protection: number;
}

export const ProtectivePutDemo = React.memo(function ProtectivePutDemo() {
  const [stockPrice, setStockPrice] = useState([100]);
  const [strikePrice, setStrikePrice] = useState([95]);
  const [premium, setPremium] = useState([2.5]);
  const [viewMode, setViewMode] = useState<'chart' | 'insurance'>('chart');

  const generateProtectivePutData = (): ProtectivePutData[] => {
    const data: ProtectivePutData[] = [];
    const currentStock = stockPrice[0];
    const strike = strikePrice[0];
    const premiumPaid = premium[0];

    for (let price = 70; price <= 130; price += 2) {
      // Stock profit/loss from current price
      const stockProfit = price - currentStock;
      
      // Put option profit (we bought the put)
      const putIntrinsic = Math.max(0, strike - price);
      const putProfit = putIntrinsic - premiumPaid;
      
      // Total protective put profit
      const totalProfit = stockProfit + putProfit;
      
      // Protection level
      const protection = Math.max(0, strike - price);

      data.push({
        stockPrice: price,
        stockProfit,
        putProfit,
        totalProfit,
        putValue: putIntrinsic,
        protection
      });
    }

    return data;
  };

  const data = generateProtectivePutData();

  const getInsuranceAnalogy = () => {
    const currentStock = stockPrice[0];
    const strike = strikePrice[0];
    const premiumPaid = premium[0];
    
    const portfolioValue = currentStock * 100; // 100 shares
    const insuranceCost = premiumPaid * 100;
    const deductible = currentStock - strike;
    const maxLoss = deductible + premiumPaid;

    return {
      portfolioValue,
      insuranceCost,
      deductible,
      maxLoss,
      insuranceRate: (insuranceCost / portfolioValue) * 100
    };
  };

  const maxLoss = () => {
    return (stockPrice[0] - strikePrice[0]) + premium[0];
  };

  const breakeven = () => {
    return stockPrice[0] + premium[0];
  };

  const insurance = getInsuranceAnalogy();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-green-500" />
          Protective Put Strategy Simulator
        </CardTitle>
        <CardDescription>
          Understand how protective puts work like portfolio insurance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Current Stock Price: ${stockPrice[0]}</label>
            <Slider
              value={stockPrice}
              onValueChange={setStockPrice}
              max={120}
              min={80}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Put Strike Price: ${strikePrice[0]}</label>
            <Slider
              value={strikePrice}
              onValueChange={setStrikePrice}
              max={stockPrice[0]}
              min={70}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Premium Paid: ${premium[0]}</label>
            <Slider
              value={premium}
              onValueChange={setPremium}
              max={8}
              min={1}
              step={0.25}
              className="w-full"
            />
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-red-50 dark:bg-red-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Max Loss</div>
            <div className="text-lg font-bold text-red-600">${maxLoss().toFixed(2)}</div>
          </div>
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Premium Paid</div>
            <div className="text-lg font-bold text-blue-600">${premium[0]}</div>
          </div>
          <div className="text-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Floor Price</div>
            <div className="text-lg font-bold text-green-600">${strikePrice[0]}</div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Breakeven</div>
            <div className="text-lg font-bold text-purple-600">${breakeven().toFixed(2)}</div>
          </div>
        </div>

        {/* View Toggle */}
        <div className="flex justify-center gap-2">
          <Button
            variant={viewMode === 'chart' ? 'default' : 'outline'}
            onClick={() => setViewMode('chart')}
            size="sm"
          >
            Payoff Chart
          </Button>
          <Button
            variant={viewMode === 'insurance' ? 'default' : 'outline'}
            onClick={() => setViewMode('insurance')}
            size="sm"
          >
            Insurance Analogy
          </Button>
        </div>

        {viewMode === 'chart' ? (
          /* Payoff Chart */
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="stockPrice" 
                  tickFormatter={(value) => `$${value}`}
                />
                <YAxis tickFormatter={(value) => `$${value}`} />
                <Tooltip 
                  formatter={(value: number, name: string) => {
                    const labels = {
                      stockProfit: 'Stock P&L',
                      putProfit: 'Put P&L',
                      totalProfit: 'Total P&L',
                      protection: 'Put Protection'
                    };
                    return [`$${value.toFixed(2)}`, labels[name as keyof typeof labels] || name];
                  }}
                  labelFormatter={(value) => `Stock Price: $${value}`}
                />
                <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
                <ReferenceLine x={stockPrice[0]} stroke="#888" strokeDasharray="2 2" label="Current" />
                <ReferenceLine x={strikePrice[0]} stroke="#10b981" strokeDasharray="4 4" label="Strike" />
                <ReferenceLine y={-maxLoss()} stroke="#ef4444" strokeDasharray="4 4" label="Max Loss" />
                
                <Area
                  type="monotone"
                  dataKey="totalProfit"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.1}
                  strokeWidth={3}
                />
                <Line 
                  type="monotone" 
                  dataKey="stockProfit" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
                <Line 
                  type="monotone" 
                  dataKey="putProfit" 
                  stroke="#ef4444" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        ) : (
          /* Insurance Analogy */
          <div className="space-y-6">
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Think of protective puts like car insurance - you pay a premium to protect against major losses.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Your Portfolio Insurance Policy</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Portfolio Value:</span>
                      <span className="font-medium">${insurance.portfolioValue.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Insurance Premium:</span>
                      <span className="font-medium">${insurance.insuranceCost.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Insurance Rate:</span>
                      <span className="font-medium">{insurance.insuranceRate.toFixed(2)}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Deductible:</span>
                      <span className="font-medium">${insurance.deductible.toFixed(2)} per share</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Maximum Loss:</span>
                      <span className="font-medium text-red-600">${(insurance.maxLoss * 100).toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">How It Works</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <div className="font-medium">Coverage Activated</div>
                        <div className="text-sm text-muted-foreground">
                          If stock falls below ${strikePrice[0]}, your put option provides protection
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <Shield className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <div className="font-medium">Guaranteed Floor</div>
                        <div className="text-sm text-muted-foreground">
                          You can sell your shares for ${strikePrice[0]} regardless of market price
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <TrendingDown className="h-5 w-5 text-orange-500 mt-0.5" />
                      <div>
                        <div className="font-medium">Premium Cost</div>
                        <div className="text-sm text-muted-foreground">
                          Like insurance, you pay ${premium[0]} per share for this protection
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">✓</div>
                <div className="font-medium text-green-700 dark:text-green-300">Stock Rises</div>
                <div className="text-sm text-muted-foreground">
                  Keep gains minus premium cost
                </div>
              </div>
              
              <div className="p-4 bg-yellow-50 dark:bg-yellow-950 rounded-lg text-center">
                <div className="text-2xl font-bold text-yellow-600 mb-1">≈</div>
                <div className="font-medium text-yellow-700 dark:text-yellow-300">Stock Sideways</div>
                <div className="text-sm text-muted-foreground">
                  Small loss from premium paid
                </div>
              </div>
              
              <div className="p-4 bg-red-50 dark:bg-red-950 rounded-lg text-center">
                <div className="text-2xl font-bold text-red-600 mb-1">⚡</div>
                <div className="font-medium text-red-700 dark:text-red-300">Stock Crashes</div>
                <div className="text-sm text-muted-foreground">
                  Protected from major losses
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Strategy Summary */}
        <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-700 dark:text-green-300 mb-2">Protective Put Summary</h4>
              <div className="text-sm text-green-600 dark:text-green-400 space-y-1">
                <p>• You own 100 shares at ${stockPrice[0]} per share</p>
                <p>• You bought 1 put option with ${strikePrice[0]} strike for ${premium[0]} premium</p>
                <p>• Maximum loss: ${maxLoss().toFixed(2)} per share (${(maxLoss() * 100).toFixed(0)} total)</p>
                <p>• Unlimited upside potential minus premium cost</p>
                <p>• Acts as portfolio insurance against significant downturns</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
