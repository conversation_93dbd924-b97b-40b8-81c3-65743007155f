
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { GraduationCap, Play, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EducationModuleProps {
  id: string;
  title: string;
  description: string;
  category: 'Market Basics' | 'Technical Analysis' | 'Risk Management' | 'Investment Strategies';
  progress: number;
  totalLessons: number;
  completedLessons: number;
  enrolledUsers: number;
  onEnroll: (id: string) => void;
  className?: string;
}

export const EducationModule = React.memo(function EducationModule({
  id,
  title,
  description,
  category,
  progress,
  totalLessons,
  completedLessons,
  enrolledUsers,
  onEnroll,
  className
}: EducationModuleProps) {
  const categoryColors = {
    'Market Basics': 'bg-blue-100 text-blue-800',
    'Technical Analysis': 'bg-purple-100 text-purple-800',
    'Risk Management': 'bg-orange-100 text-orange-800',
    'Investment Strategies': 'bg-emerald-100 text-emerald-800'
  };

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <GraduationCap className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <Badge className={categoryColors[category]} variant="secondary">
            {category}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{completedLessons}/{totalLessons} lessons</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Users className="h-4 w-4" />
            {enrolledUsers.toLocaleString()} enrolled
          </div>
          <Button 
            onClick={() => onEnroll(id)}
            size="sm"
            className="gap-2"
          >
            <Play className="h-4 w-4" />
            {progress > 0 ? "Continue" : "Start Module"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});
