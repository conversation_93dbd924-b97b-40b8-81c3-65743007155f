
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { FinancialData, CashFlowData, BalanceSheetData } from '@/types/stock';
import { IncomeTab } from './IncomeTab';
import { BalanceSheetTab } from './BalanceSheetTab';
import { CashFlowTab } from './CashFlowTab';

interface FinancialMetricsProps {
  symbol: string;
  financialData: FinancialData[];
  cashFlowData: CashFlowData[];
  balanceSheetData: BalanceSheetData[];
  className?: string;
}

export function FinancialMetrics({ 
  symbol, 
  financialData, 
  cashFlowData, 
  balanceSheetData, 
  className 
}: FinancialMetricsProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{symbol} Financial Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="income">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="income">Income Statement</TabsTrigger>
            <TabsTrigger value="balance">Balance Sheet</TabsTrigger>
            <TabsTrigger value="cashflow">Cash Flow</TabsTrigger>
          </TabsList>
          
          <TabsContent value="income">
            <IncomeTab financialData={financialData} />
          </TabsContent>
          
          <TabsContent value="balance">
            <BalanceSheetTab balanceSheetData={balanceSheetData} />
          </TabsContent>
          
          <TabsContent value="cashflow">
            <CashFlowTab cashFlowData={cashFlowData} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

export type { FinancialMetricsProps };
