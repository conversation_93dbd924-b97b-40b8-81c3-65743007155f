
export interface ProspectiveStock {
  symbol: string;
  name: string;
  market: string;
  region: string;
  sector: string;
  industry: string;
  price: number;
  change: number;
  changePercent: number;
  marketCap: number;
  volume: number;
  pe: number;
  eps: number;
  dividend: number;
  beta: number;
  fiftyTwoWeekHigh: number;
  fiftyTwoWeekLow: number;
  analystRating: number; // Rating from 1-5
  growthScore: number; // Score from 1-100
  valueScore: number; // Score from 1-100
  momentumScore: number; // Score from 1-100
  overallScore: number; // Combined score from 1-100
}

export type MarketRegion = "US" | "Europe" | "Asia" | "Australia" | "China" | "Middle East" | "South America" | "Africa";

export interface FilterOptions {
  regions: MarketRegion[];
  sectors: string[];
  minMarketCap?: number;
  maxPE?: number;
  minDividend?: number;
  minGrowthScore?: number;
  minValueScore?: number;
  minMomentumScore?: number;
  minOverallScore?: number;
}
