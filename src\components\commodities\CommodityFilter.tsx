
import { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { 
  CommodityFilterOptions,
  CommodityCategory,
  RiskLevel,
  InvestmentHorizon
} from '@/types/commodities';
import { commodityCategories, riskLevels, investmentHorizons } from '@/utils/commoditiesData';

interface CommodityFilterProps {
  onFilterChange: (filters: CommodityFilterOptions) => void;
}

export function CommodityFilter({ onFilterChange }: CommodityFilterProps) {
  const [selectedCategories, setSelectedCategories] = useState<CommodityCategory[]>([]);
  const [minOverallScore, setMinOverallScore] = useState<number>(50);
  const [selectedRiskLevels, setSelectedRiskLevels] = useState<RiskLevel[]>([]);
  const [minRiskBenefitRatio, setMinRiskBenefitRatio] = useState<number>(0);
  const [selectedHorizons, setSelectedHorizons] = useState<InvestmentHorizon[]>([]);
  
  const handleCategoryToggle = (category: CommodityCategory) => {
    setSelectedCategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(c => c !== category);
      } else {
        return [...prev, category];
      }
    });
  };

  const handleRiskLevelToggle = (risk: RiskLevel) => {
    setSelectedRiskLevels(prev => {
      if (prev.includes(risk)) {
        return prev.filter(r => r !== risk);
      } else {
        return [...prev, risk];
      }
    });
  };
  
  const handleHorizonToggle = (horizon: InvestmentHorizon) => {
    setSelectedHorizons(prev => {
      if (prev.includes(horizon)) {
        return prev.filter(h => h !== horizon);
      } else {
        return [...prev, horizon];
      }
    });
  };
  
  const handleApplyFilters = () => {
    onFilterChange({
      categories: selectedCategories.length > 0 ? selectedCategories : undefined,
      minOverallScore: minOverallScore > 0 ? minOverallScore : undefined,
      maxRiskLevel: selectedRiskLevels.length > 0 ? 
        selectedRiskLevels[selectedRiskLevels.length - 1] : 
        undefined,
      minRiskBenefitRatio: minRiskBenefitRatio > 0 ? minRiskBenefitRatio : undefined,
      investmentHorizons: selectedHorizons.length > 0 ? selectedHorizons : undefined
    });
  };
  
  const handleReset = () => {
    setSelectedCategories([]);
    setMinOverallScore(50);
    setSelectedRiskLevels([]);
    setMinRiskBenefitRatio(0);
    setSelectedHorizons([]);
    
    onFilterChange({});
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle>Filter Commodities</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="space-y-4">
            <div className="font-medium">Categories</div>
            <div className="flex flex-wrap gap-2">
              {commodityCategories.map((category) => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`category-${category}`}
                    checked={selectedCategories.includes(category)} 
                    onCheckedChange={() => handleCategoryToggle(category)}
                  />
                  <Label htmlFor={`category-${category}`}>{category}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="font-medium">Risk Levels</div>
            <div className="flex flex-col gap-2">
              {riskLevels.map((risk) => (
                <div key={risk} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`risk-${risk}`}
                    checked={selectedRiskLevels.includes(risk)} 
                    onCheckedChange={() => handleRiskLevelToggle(risk)}
                  />
                  <Label htmlFor={`risk-${risk}`}>{risk}</Label>
                </div>
              ))}
            </div>
            
            <div className="font-medium mt-4">Investment Horizon</div>
            <div className="flex flex-col gap-2">
              {investmentHorizons.map((horizon) => (
                <div key={horizon} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`horizon-${horizon}`}
                    checked={selectedHorizons.includes(horizon)} 
                    onCheckedChange={() => handleHorizonToggle(horizon)}
                  />
                  <Label htmlFor={`horizon-${horizon}`}>{horizon}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="font-medium">Minimum Overall Score</div>
            <div className="px-2">
              <Slider 
                value={[minOverallScore]} 
                min={0} 
                max={100} 
                step={5}
                onValueChange={(values) => setMinOverallScore(values[0])} 
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0</span>
                <span>{minOverallScore}+</span>
                <span>100</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="font-medium">Minimum Risk/Benefit Ratio</div>
            <div className="px-2">
              <Slider 
                value={[minRiskBenefitRatio]} 
                min={0} 
                max={6} 
                step={0.5}
                onValueChange={(values) => setMinRiskBenefitRatio(values[0])} 
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0</span>
                <span>{minRiskBenefitRatio}+</span>
                <span>6+</span>
              </div>
            </div>
            
            <div className="flex justify-end gap-2 mt-8">
              <Button variant="outline" onClick={handleReset}>Reset</Button>
              <Button onClick={handleApplyFilters}>Apply Filters</Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
