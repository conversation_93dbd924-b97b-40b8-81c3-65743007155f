
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Area, AreaChart } from 'recharts';
import { TrendingUp, TrendingDown, BarChart3, Target } from 'lucide-react';

interface StrategyData {
  stockPrice: number;
  profit: number;
  leg1: number;
  leg2: number;
  leg3?: number;
  leg4?: number;
}

export const ComplexStrategiesDemo = React.memo(function ComplexStrategiesDemo() {
  const [activeStrategy, setActiveStrategy] = useState('bull-call-spread');
  const [stockPrice, setStockPrice] = useState([100]);
  const [param1, setParam1] = useState([95]); // Lower strike for spreads
  const [param2, setParam2] = useState([105]); // Higher strike for spreads
  const [param3, setParam3] = useState([2]); // Premium 1
  const [param4, setParam4] = useState([1]); // Premium 2

  const strategies = {
    'bull-call-spread': {
      name: 'Bull Call Spread',
      description: 'Buy low strike call, sell high strike call',
      icon: <TrendingUp className="h-4 w-4" />,
      params: [
        { label: 'Lower Strike', value: param1, setter: setParam1, min: 85, max: 110 },
        { label: 'Higher Strike', value: param2, setter: setParam2, min: 90, max: 115 },
        { label: 'Lower Premium', value: param3, setter: setParam3, min: 1, max: 8 },
        { label: 'Higher Premium', value: param4, setter: setParam4, min: 0.5, max: 5 }
      ]
    },
    'iron-condor': {
      name: 'Iron Condor',
      description: 'Sell put spread + sell call spread',
      icon: <BarChart3 className="h-4 w-4" />,
      params: [
        { label: 'Put Strike Low', value: param1, setter: setParam1, min: 85, max: 95 },
        { label: 'Call Strike High', value: param2, setter: setParam2, min: 105, max: 115 },
        { label: 'Net Premium', value: param3, setter: setParam3, min: 1, max: 5 },
        { label: 'Strike Width', value: param4, setter: setParam4, min: 5, max: 15 }
      ]
    },
    'long-straddle': {
      name: 'Long Straddle',
      description: 'Buy call and put at same strike',
      icon: <Target className="h-4 w-4" />,
      params: [
        { label: 'Strike Price', value: param1, setter: setParam1, min: 90, max: 110 },
        { label: 'Call Premium', value: param2, setter: setParam2, min: 2, max: 8 },
        { label: 'Put Premium', value: param3, setter: setParam3, min: 2, max: 8 },
        { label: 'Total Premium', value: param4, setter: setParam4, min: 4, max: 16 }
      ]
    },
    'butterfly': {
      name: 'Butterfly Spread',
      description: 'Buy 1-2-1 ratio at three strikes',
      icon: <TrendingDown className="h-4 w-4" />,
      params: [
        { label: 'Low Strike', value: param1, setter: setParam1, min: 90, max: 95 },
        { label: 'High Strike', value: param2, setter: setParam2, min: 105, max: 110 },
        { label: 'Net Premium', value: param3, setter: setParam3, min: 0.5, max: 3 },
        { label: 'Middle Strike', value: param4, setter: setParam4, min: 98, max: 102 }
      ]
    }
  };

  const generateStrategyData = (): StrategyData[] => {
    const data: StrategyData[] = [];
    
    for (let price = 75; price <= 125; price += 1) {
      let profit = 0;
      let leg1 = 0, leg2 = 0, leg3 = 0, leg4 = 0;

      switch (activeStrategy) {
        case 'bull-call-spread':
          // Buy call at param1, sell call at param2
          leg1 = Math.max(0, price - param1[0]) - param3[0]; // Long call
          leg2 = -(Math.max(0, price - param2[0]) - param4[0]); // Short call
          profit = leg1 + leg2;
          break;

        case 'iron-condor':
          // Short put spread + short call spread
          const putStrikeLow = param1[0];
          const callStrikeHigh = param2[0];
          const putStrikeHigh = stockPrice[0] - 5;
          const callStrikeLow = stockPrice[0] + 5;
          const condorPremium = param3[0];
          
          leg1 = -(Math.max(0, putStrikeHigh - price)); // Short put high
          leg2 = Math.max(0, putStrikeLow - price); // Long put low
          leg3 = -(Math.max(0, price - callStrikeLow)); // Short call low
          leg4 = Math.max(0, price - callStrikeHigh); // Long call high
          
          profit = leg1 + leg2 + leg3 + leg4 + condorPremium;
          break;

        case 'long-straddle':
          // Buy call and put at same strike
          const strike = param1[0];
          const callPremium = param2[0];
          const putPremium = param3[0];
          
          leg1 = Math.max(0, price - strike) - callPremium; // Long call
          leg2 = Math.max(0, strike - price) - putPremium; // Long put
          profit = leg1 + leg2;
          break;

        case 'butterfly':
          // Buy low, sell 2x middle, buy high
          const lowStrike = param1[0];
          const highStrike = param2[0];
          const middleStrike = param4[0];
          const butterflyPremium = param3[0];
          
          leg1 = Math.max(0, price - lowStrike); // Long low call
          leg2 = -2 * Math.max(0, price - middleStrike); // Short 2x middle calls
          leg3 = Math.max(0, price - highStrike); // Long high call
          
          profit = leg1 + leg2 + leg3 - butterflyPremium;
          break;
      }

      data.push({
        stockPrice: price,
        profit,
        leg1,
        leg2,
        leg3,
        leg4
      });
    }

    return data;
  };

  const data = generateStrategyData();
  const currentStrategy = strategies[activeStrategy as keyof typeof strategies];

  const getMaxProfit = () => {
    return Math.max(...data.map(d => d.profit));
  };

  const getMaxLoss = () => {
    return Math.min(...data.map(d => d.profit));
  };

  const getBreakevens = () => {
    const breakevens: number[] = [];
    for (let i = 0; i < data.length - 1; i++) {
      const current = data[i];
      const next = data[i + 1];
      
      if ((current.profit <= 0 && next.profit > 0) || (current.profit > 0 && next.profit <= 0)) {
        // Linear interpolation for more precise breakeven
        const ratio = Math.abs(current.profit) / (Math.abs(current.profit) + Math.abs(next.profit));
        const breakeven = current.stockPrice + ratio * (next.stockPrice - current.stockPrice);
        breakevens.push(breakeven);
      }
    }
    return breakevens;
  };

  const maxProfit = getMaxProfit();
  const maxLoss = getMaxLoss();
  const breakevens = getBreakevens();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Complex Options Strategies</CardTitle>
        <CardDescription>
          Explore advanced multi-leg strategies for different market scenarios
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Strategy Selection */}
        <Tabs value={activeStrategy} onValueChange={setActiveStrategy}>
          <TabsList className="grid w-full grid-cols-4">
            {Object.entries(strategies).map(([key, strategy]) => (
              <TabsTrigger key={key} value={key} className="flex items-center gap-1 text-xs">
                {strategy.icon}
                <span className="hidden sm:inline">{strategy.name.split(' ')[0]}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(strategies).map(([key, strategy]) => (
            <TabsContent key={key} value={key} className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold flex items-center justify-center gap-2">
                  {strategy.icon}
                  {strategy.name}
                </h3>
                <p className="text-sm text-muted-foreground">{strategy.description}</p>
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Parameters */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {currentStrategy.params.map((param, index) => (
            <div key={index} className="space-y-2">
              <label className="text-sm font-medium">
                {param.label}: {param.value[0]}{param.label.includes('Premium') || param.label.includes('Strike') ? '$' : ''}
              </label>
              <Slider
                value={param.value}
                onValueChange={param.setter}
                max={param.max}
                min={param.min}
                step={param.label.includes('Premium') ? 0.25 : 1}
                className="w-full"
              />
            </div>
          ))}
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Max Profit</div>
            <div className="text-lg font-bold text-green-600">
              {maxProfit > 0 ? `$${maxProfit.toFixed(2)}` : 'Unlimited'}
            </div>
          </div>
          <div className="text-center p-3 bg-red-50 dark:bg-red-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Max Loss</div>
            <div className="text-lg font-bold text-red-600">
              {maxLoss < 0 ? `$${Math.abs(maxLoss).toFixed(2)}` : '$0'}
            </div>
          </div>
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Breakevens</div>
            <div className="text-lg font-bold text-blue-600">
              {breakevens.length}
            </div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
            <div className="text-sm text-muted-foreground">Risk/Reward</div>
            <div className="text-lg font-bold text-purple-600">
              {maxLoss < 0 && maxProfit > 0 ? 
                (maxProfit / Math.abs(maxLoss)).toFixed(2) : 
                'N/A'
              }
            </div>
          </div>
        </div>

        {/* Payoff Chart */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="stockPrice" 
                tickFormatter={(value) => `$${value}`}
              />
              <YAxis tickFormatter={(value) => `$${value}`} />
              <Tooltip 
                formatter={(value: number, name: string) => {
                  const labels = {
                    profit: 'Total P&L',
                    leg1: 'Leg 1 P&L',
                    leg2: 'Leg 2 P&L',
                    leg3: 'Leg 3 P&L',
                    leg4: 'Leg 4 P&L'
                  };
                  return [`$${value.toFixed(2)}`, labels[name as keyof typeof labels] || name];
                }}
                labelFormatter={(value) => `Stock Price: $${value}`}
              />
              <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
              <ReferenceLine x={stockPrice[0]} stroke="#888" strokeDasharray="2 2" label="Current" />
              
              {/* Breakeven lines */}
              {breakevens.map((breakeven, index) => (
                <ReferenceLine 
                  key={index}
                  x={breakeven} 
                  stroke="#f59e0b" 
                  strokeDasharray="4 4" 
                  label={`BE${index + 1}`} 
                />
              ))}
              
              <Area
                type="monotone"
                dataKey="profit"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.1}
                strokeWidth={3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Strategy Analysis */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="p-4">
            <h4 className="font-medium mb-3">Market Outlook</h4>
            <div className="space-y-2 text-sm">
              {activeStrategy === 'bull-call-spread' && (
                <>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span>Moderately Bullish</span>
                  </div>
                  <p className="text-muted-foreground">
                    Profits from moderate upward movement with limited risk and reward.
                  </p>
                </>
              )}
              {activeStrategy === 'iron-condor' && (
                <>
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                    <span>Neutral/Low Volatility</span>
                  </div>
                  <p className="text-muted-foreground">
                    Profits when stock stays within range and volatility remains low.
                  </p>
                </>
              )}
              {activeStrategy === 'long-straddle' && (
                <>
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-orange-500" />
                    <span>High Volatility Expected</span>
                  </div>
                  <p className="text-muted-foreground">
                    Profits from large moves in either direction, regardless of bias.
                  </p>
                </>
              )}
              {activeStrategy === 'butterfly' && (
                <>
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-purple-500" />
                    <span>Very Neutral</span>
                  </div>
                  <p className="text-muted-foreground">
                    Profits when stock stays very close to middle strike price.
                  </p>
                </>
              )}
            </div>
          </Card>

          <Card className="p-4">
            <h4 className="font-medium mb-3">Key Levels</h4>
            <div className="space-y-2 text-sm">
              {breakevens.map((breakeven, index) => (
                <div key={index} className="flex justify-between">
                  <span>Breakeven {index + 1}:</span>
                  <span className="font-medium">${breakeven.toFixed(2)}</span>
                </div>
              ))}
              <div className="flex justify-between">
                <span>Max Profit:</span>
                <span className="font-medium text-green-600">
                  {maxProfit > 0 ? `$${maxProfit.toFixed(2)}` : 'Unlimited'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Max Loss:</span>
                <span className="font-medium text-red-600">
                  {maxLoss < 0 ? `$${Math.abs(maxLoss).toFixed(2)}` : '$0'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Profit Zone:</span>
                <span className="font-medium">
                  {breakevens.length === 2 ? 
                    `$${breakevens[0].toFixed(0)} - $${breakevens[1].toFixed(0)}` :
                    `Beyond $${breakevens[0]?.toFixed(0) || 'N/A'}`
                  }
                </span>
              </div>
            </div>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
});
