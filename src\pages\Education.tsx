
import React from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { TutorialCard } from '@/components/education/TutorialCard';
import { EducationModule } from '@/components/education/EducationModule';
import { RiskGuideCard } from '@/components/education/RiskGuideCard';
import { StrategyCard } from '@/components/education/StrategyCard';
import { TutorialRouter } from '@/components/education/TutorialRouter';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useEducation } from '@/hooks/useEducation';
import { GraduationCap, BookOpen, Shield, Target, Filter } from 'lucide-react';

const Education = () => {
  const {
    tutorials,
    modules,
    riskGuides,
    strategies,
    isLoading,
    activeFilter,
    activeTutorial,
    setActiveFilter,
    handleStartTutorial,
    handleCompleteTutorial,
    handleExitTutorial,
    handleEnrollModule,
    handleReadGuide,
    handleLearnStrategy
  } = useEducation();

  const filterOptions = [
    { value: 'all', label: 'All Levels' },
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' }
  ];

  // If a tutorial is active, show the tutorial router
  if (activeTutorial) {
    return (
      <PageLayout title="Tutorial">
        <TutorialRouter
          tutorialId={activeTutorial}
          onComplete={() => handleCompleteTutorial(activeTutorial)}
          onExit={handleExitTutorial}
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout title="Education Center">
      <div className="space-y-6">
        {/* Header Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GraduationCap className="h-6 w-6" />
              Welcome to OptiVestor Education Center
            </CardTitle>
            <CardDescription>
              Master the art of investing with our comprehensive educational resources. 
              From beginner tutorials to advanced strategies, we've got you covered.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{tutorials.length}</div>
                <div className="text-sm text-muted-foreground">Interactive Tutorials</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{modules.length}</div>
                <div className="text-sm text-muted-foreground">Education Modules</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{riskGuides.length}</div>
                <div className="text-sm text-muted-foreground">Risk Guides</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{strategies.length}</div>
                <div className="text-sm text-muted-foreground">Investment Strategies</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="tutorials" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tutorials" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Tutorials
            </TabsTrigger>
            <TabsTrigger value="modules" className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4" />
              Modules
            </TabsTrigger>
            <TabsTrigger value="risk-guides" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Risk Guides
            </TabsTrigger>
            <TabsTrigger value="strategies" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Strategies
            </TabsTrigger>
          </TabsList>

          {/* Interactive Tutorials */}
          <TabsContent value="tutorials" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Interactive Tutorials</h2>
                <p className="text-muted-foreground">
                  Step-by-step guided tutorials to build your investment knowledge
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <div className="flex gap-2">
                  {filterOptions.map(option => (
                    <Button
                      key={option.value}
                      variant={activeFilter === option.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => setActiveFilter(option.value)}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tutorials.map(tutorial => (
                <TutorialCard
                  key={tutorial.id}
                  {...tutorial}
                  onStart={handleStartTutorial}
                />
              ))}
            </div>
          </TabsContent>

          {/* Education Modules */}
          <TabsContent value="modules" className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold">Education Modules</h2>
              <p className="text-muted-foreground">
                Comprehensive courses covering all aspects of investing and trading
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {modules.map(module => (
                <EducationModule
                  key={module.id}
                  {...module}
                  onEnroll={handleEnrollModule}
                />
              ))}
            </div>
          </TabsContent>

          {/* Risk Management Guides */}
          <TabsContent value="risk-guides" className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold">Risk Management Guides</h2>
              <p className="text-muted-foreground">
                Essential guides to help you understand and manage investment risks
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {riskGuides.map(guide => (
                <RiskGuideCard
                  key={guide.id}
                  {...guide}
                  onRead={handleReadGuide}
                />
              ))}
            </div>
          </TabsContent>

          {/* Investment Strategies */}
          <TabsContent value="strategies" className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold">Investment Strategies</h2>
              <p className="text-muted-foreground">
                Learn about different investment approaches and find the one that suits you
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {strategies.map(strategy => (
                <StrategyCard
                  key={strategy.id}
                  {...strategy}
                  onLearnMore={handleLearnStrategy}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
};

export default Education;
