
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Target, BarChart3, DollarSign, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StrategyCardProps {
  id: string;
  title: string;
  description: string;
  strategyType: 'Conservative' | 'Moderate' | 'Aggressive' | 'Balanced';
  expectedReturn: string;
  timeHorizon: string;
  minimumInvestment: string;
  suitableFor: string[];
  onLearnMore: (id: string) => void;
  className?: string;
}

export const StrategyCard = React.memo(function StrategyCard({
  id,
  title,
  description,
  strategyType,
  expectedReturn,
  timeHorizon,
  minimumInvestment,
  suitableFor,
  onLearnMore,
  className
}: StrategyCardProps) {
  const strategyColors = {
    Conservative: 'bg-blue-100 text-blue-800',
    Moderate: 'bg-green-100 text-green-800',
    Aggressive: 'bg-red-100 text-red-800',
    Balanced: 'bg-purple-100 text-purple-800'
  };

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <Badge className={strategyColors[strategyType]} variant="secondary">
            {strategyType}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">Expected Return</p>
              <p className="text-muted-foreground">{expectedReturn}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">Time Horizon</p>
              <p className="text-muted-foreground">{timeHorizon}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="font-medium">Min. Investment</p>
              <p className="text-muted-foreground">{minimumInvestment}</p>
            </div>
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">Suitable for:</h4>
          <div className="flex flex-wrap gap-1">
            {suitableFor.map((item, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {item}
              </Badge>
            ))}
          </div>
        </div>
        
        <Button onClick={() => onLearnMore(id)} className="w-full" size="sm">
          Learn More
        </Button>
      </CardContent>
    </Card>
  );
});
