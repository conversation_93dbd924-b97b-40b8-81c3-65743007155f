
import { 
  ComposedChart, Bar, Line, XAxis, YAxis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, ReferenceLine
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CashFlowChartProps {
  data: {
    period: string;
    operating: number;
    investing: number;
    financing: number;
    freeCashFlow: number;
  }[];
}

export const CashFlowChart = ({ data }: CashFlowChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Cash Flow (Billions)</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={data}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="period" />
            <YAxis />
            <Tooltip 
              formatter={(value: number) => [`$${value.toFixed(2)}B`, '']}
            />
            <Legend />
            <ReferenceLine 
              y={0} 
              stroke="hsl(var(--border))" 
              strokeWidth={1}
              yAxisId="auto"
            />
            <Bar 
              dataKey="operating" 
              fill="hsl(var(--success))" 
              name="Operating CF" 
              barSize={20}
            />
            <Bar 
              dataKey="investing" 
              fill="hsl(var(--warning))" 
              name="Investing CF" 
              barSize={20}
            />
            <Bar 
              dataKey="financing" 
              fill="hsl(var(--danger))" 
              name="Financing CF" 
              barSize={20}
            />
            <Line 
              type="monotone" 
              dataKey="freeCashFlow" 
              stroke="hsl(var(--primary))"
              strokeWidth={2}
              name="Free Cash Flow" 
            />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
