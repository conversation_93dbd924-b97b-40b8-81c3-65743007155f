
import React, { useState } from 'react';
import { Navbar } from '@/components/layout/Navbar';
import { Sidebar } from '@/components/layout/Sidebar';
import { FocusManager } from '@/components/accessibility/FocusManager';
import { PWAInstallButton } from '@/components/common/PWAInstallButton';
import { usePWA } from '@/hooks/usePWA';

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  onSearch?: (term: string) => void;
}

export function PageLayout({ children, title, onSearch }: PageLayoutProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const { isOnline } = usePWA();
  
  const toggleSidebar = () => {
    setIsSidebarCollapsed(prev => !prev);
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar onSearch={onSearch}>
        <PWAInstallButton className="ml-2" />
        {!isOnline && (
          <div 
            className="ml-2 px-2 py-1 bg-warning text-warning-foreground text-xs rounded"
            role="alert"
            aria-label="Offline mode active"
          >
            Offline
          </div>
        )}
      </Navbar>
      
      <div className="flex-1 flex">
        <Sidebar isCollapsed={isSidebarCollapsed} onToggle={toggleSidebar} />
        
        <FocusManager>
          <main 
            className="flex-1 transition-all duration-300"
            role="main"
            aria-label={`${title} page content`}
            id="main-content"
            tabIndex={-1}
          >
            <div className="container max-w-full p-4 lg:p-6 animate-fade-in">
              <h1 
                className="text-2xl font-bold mb-6"
                id="page-title"
                tabIndex={-1}
              >
                {title}
              </h1>
              {children}
            </div>
          </main>
        </FocusManager>
      </div>
    </div>
  );
}
