import React, { memo } from 'react';
import { StatsCard } from '@/components/ui/StatsCard';
import { DollarSign, TrendingUp, TrendingDown, Wallet } from 'lucide-react';

interface TopMoverData {
  symbol: string;
  name: string;
  change: number;
}

interface MarketStatsData {
  totalMarketCap: string;
  tradingVolume: string;
  topGainer: TopMoverData;
  topLoser: TopMoverData;
}

interface MarketStatsProps {
  data: MarketStatsData;
  isLoading?: boolean;
  error?: string | null;
}

const MarketStatsComponent = memo(function MarketStats({ 
  data, 
  isLoading = false, 
  error 
}: MarketStatsProps) {
  if (error) {
    return (
      <div 
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
        role="alert"
        aria-label="Market statistics error"
      >
        <div className="col-span-full text-center text-destructive p-4">
          Error loading market stats: {error}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div 
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
        aria-label="Loading market statistics"
      >
        {[...Array(4)].map((_, index) => (
          <div 
            key={index} 
            className="animate-pulse bg-muted rounded-lg h-24"
            aria-label={`Loading stat ${index + 1}`}
          />
        ))}
      </div>
    );
  }

  return (
    <div 
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
      role="region"
      aria-label="Market statistics"
    >
      <StatsCard 
        title="Total Market Cap" 
        value={data.totalMarketCap}
        trend={0.62}
        icon={<Wallet className="h-4 w-4" />}
        aria-label={`Total market cap: ${data.totalMarketCap}, up 0.62%`}
      />
      <StatsCard 
        title="Trading Volume" 
        value={data.tradingVolume}
        description="Today's volume"
        icon={<DollarSign className="h-4 w-4" />}
        aria-label={`Trading volume: ${data.tradingVolume}`}
      />
      <StatsCard 
        title="Top Gainer" 
        value={data.topGainer.symbol}
        trend={data.topGainer.change}
        trendLabel={data.topGainer.name}
        icon={<TrendingUp className="h-4 w-4" />}
        aria-label={`Top gainer: ${data.topGainer.symbol} (${data.topGainer.name}), up ${data.topGainer.change}%`}
      />
      <StatsCard 
        title="Top Loser" 
        value={data.topLoser.symbol}
        trend={data.topLoser.change}
        trendLabel={data.topLoser.name}
        icon={<TrendingDown className="h-4 w-4" />}
        aria-label={`Top loser: ${data.topLoser.symbol} (${data.topLoser.name}), down ${Math.abs(data.topLoser.change)}%`}
      />
    </div>
  );
});

export { MarketStatsComponent as MarketStats };
export type { MarketStatsData, MarketStatsProps, TopMoverData };
