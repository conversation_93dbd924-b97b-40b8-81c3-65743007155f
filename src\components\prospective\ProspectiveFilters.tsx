
import { useState } from 'react';
import { 
  Card, 
  CardContent, 
  Card<PERSON>eader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { FilterOptions, MarketRegion } from '@/types/prospectiveStocks';
import { sectors, regions } from '@/utils/prospectiveStocksData';
import { Button } from '@/components/ui/button';

interface ProspectiveFiltersProps {
  onFilterChange: (filters: FilterOptions) => void;
}

export function ProspectiveFilters({ onFilterChange }: ProspectiveFiltersProps) {
  const [selectedRegions, setSelectedRegions] = useState<MarketRegion[]>(["US"]);
  const [selectedSectors, setSelectedSectors] = useState<string[]>([]);
  const [minMarketCap, setMinMarketCap] = useState<number>(0);
  const [maxPE, setMaxPE] = useState<number>(50);
  const [minDividend, setMinDividend] = useState<number>(0);
  const [minOverallScore, setMinOverallScore] = useState<number>(50);
  
  const handleRegionToggle = (region: MarketRegion) => {
    setSelectedRegions(prev => {
      if (prev.includes(region)) {
        return prev.filter(r => r !== region);
      } else {
        return [...prev, region];
      }
    });
  };

  const handleSectorToggle = (sector: string) => {
    setSelectedSectors(prev => {
      if (prev.includes(sector)) {
        return prev.filter(s => s !== sector);
      } else {
        return [...prev, sector];
      }
    });
  };
  
  const handleApplyFilters = () => {
    onFilterChange({
      regions: selectedRegions.length > 0 ? selectedRegions : regions,
      sectors: selectedSectors,
      minMarketCap: minMarketCap * 1000000000, // Convert to billions
      maxPE: maxPE > 0 ? maxPE : undefined,
      minDividend: minDividend > 0 ? minDividend : undefined,
      minOverallScore
    });
  };
  
  const handleReset = () => {
    setSelectedRegions(["US"]);
    setSelectedSectors([]);
    setMinMarketCap(0);
    setMaxPE(50);
    setMinDividend(0);
    setMinOverallScore(50);
    
    onFilterChange({
      regions: ["US"],
      sectors: []
    });
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle>Filter Prospective Stocks</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="space-y-4">
            <div className="font-medium">Markets</div>
            <div className="flex flex-wrap gap-2">
              {regions.map((region) => (
                <div key={region} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`region-${region}`}
                    checked={selectedRegions.includes(region)} 
                    onCheckedChange={() => handleRegionToggle(region)}
                  />
                  <Label htmlFor={`region-${region}`}>{region}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="font-medium">Sectors</div>
            <div className="flex flex-col gap-2 max-h-40 overflow-y-auto">
              {sectors.map((sector) => (
                <div key={sector} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`sector-${sector}`}
                    checked={selectedSectors.includes(sector)} 
                    onCheckedChange={() => handleSectorToggle(sector)}
                  />
                  <Label htmlFor={`sector-${sector}`}>{sector}</Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="font-medium">Market Cap (Billions)</div>
            <div className="px-2">
              <Slider 
                value={[minMarketCap]} 
                min={0} 
                max={1000} 
                step={10}
                onValueChange={(values) => setMinMarketCap(values[0])} 
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>$0B</span>
                <span>${minMarketCap}B+</span>
                <span>$1000B</span>
              </div>
            </div>
            
            <div className="font-medium mt-4">P/E Ratio (Max)</div>
            <div className="px-2">
              <Slider 
                value={[maxPE]} 
                min={0} 
                max={100} 
                step={1}
                onValueChange={(values) => setMaxPE(values[0])} 
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>No limit</span>
                <span>{maxPE > 0 ? maxPE : 'Any'}</span>
                <span>100</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="font-medium">Minimum Dividend</div>
            <div className="px-2">
              <Slider 
                value={[minDividend]} 
                min={0} 
                max={10} 
                step={0.5}
                onValueChange={(values) => setMinDividend(values[0])} 
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0%</span>
                <span>{minDividend}%+</span>
                <span>10%</span>
              </div>
            </div>
            
            <div className="font-medium mt-4">Minimum Overall Score</div>
            <div className="px-2">
              <Slider 
                value={[minOverallScore]} 
                min={0} 
                max={100} 
                step={5}
                onValueChange={(values) => setMinOverallScore(values[0])} 
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0</span>
                <span>{minOverallScore}+</span>
                <span>100</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={handleReset}>Reset</Button>
          <Button onClick={handleApplyFilters}>Apply Filters</Button>
        </div>
      </CardContent>
    </Card>
  );
}
