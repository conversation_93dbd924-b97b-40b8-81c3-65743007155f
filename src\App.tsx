
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AccessibilityProvider } from "@/contexts/AccessibilityContext";
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Stocks from "./pages/Stocks";
import Financial from "./pages/Financial";
import Predictions from "./pages/Predictions";
import PredictionAlgorithms from "./pages/PredictionAlgorithms";
import Markets from "./pages/Markets";
import Watchlist from "./pages/Watchlist";
import News from "./pages/News";
import Settings from "./pages/Settings";
import Global from "./pages/Global";
import ProspectiveStocks from "./pages/ProspectiveStocks";
import CommoditiesAnalysis from "./pages/CommoditiesAnalysis";
import Education from "./pages/Education";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <AccessibilityProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/stocks" element={<Stocks />} />
                <Route path="/financial" element={<Financial />} />
                <Route path="/predictions" element={<Predictions />} />
                <Route path="/prediction-algorithms" element={<PredictionAlgorithms />} />
                <Route path="/markets" element={<Markets />} />
                <Route path="/global" element={<Global />} />
                <Route path="/prospective-stocks" element={<ProspectiveStocks />} />
                <Route path="/commodities" element={<CommoditiesAnalysis />} />
                <Route path="/watchlist" element={<Watchlist />} />
                <Route path="/news" element={<News />} />
                <Route path="/education" element={<Education />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </AccessibilityProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
