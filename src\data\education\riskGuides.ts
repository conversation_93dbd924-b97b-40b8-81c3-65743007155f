
import { RiskGuide } from '@/types/education';

export const riskGuidesData: RiskGuide[] = [
  {
    id: 'portfolio-diversification',
    title: 'Portfolio Diversification',
    description: 'Learn how to spread risk across different assets and sectors.',
    riskLevel: 'Low',
    keyPoints: [
      'Asset allocation strategies',
      'Sector diversification',
      'Geographic diversification',
      'Rebalancing techniques'
    ],
    readTime: '10 min'
  },
  {
    id: 'volatility-management',
    title: 'Managing Market Volatility',
    description: 'Strategies to handle market ups and downs without panic selling.',
    riskLevel: 'Medium',
    keyPoints: [
      'Understanding market cycles',
      'Dollar-cost averaging',
      'Stop-loss strategies',
      'Emotional discipline'
    ],
    readTime: '12 min'
  },
  {
    id: 'leverage-risks',
    title: 'Understanding Leverage Risks',
    description: 'Critical information about margin trading and leveraged investments.',
    riskLevel: 'High',
    keyPoints: [
      'Margin call mechanics',
      'Amplified losses',
      'Interest costs',
      'Risk mitigation strategies'
    ],
    readTime: '15 min'
  }
];
