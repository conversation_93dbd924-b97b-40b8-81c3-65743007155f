
export interface Stock {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  pe: number;
  dividend: number;
  eps: number;
  high52: number;
  low52: number;
  beta: number;
  lastUpdated: Date;
}

export interface FinancialData {
  symbol: string;
  period: string;
  revenue: number;
  netIncome: number;
  ebitda: number;
  grossMargin: number;
  operatingMargin: number;
  netMargin: number;
  eps: number;
}

export interface BalanceSheetData {
  symbol: string;
  period: string;
  totalAssets: number;
  totalLiabilities: number;
  totalEquity: number;
  cashAndEquivalents: number;
  shortTermDebt: number;
  longTermDebt: number;
  debtToEquityRatio: number;
}

export interface CashFlowData {
  symbol: string;
  period: string;
  operatingCashFlow: number;
  investingCashFlow: number;
  financingCashFlow: number;
  freeCashFlow: number;
  capex: number;
  dividendsPaid: number;
}

export interface ValuationMetrics {
  symbol: string;
  pe: number;
  pegRatio: number;
  pb: number;
  ps: number;
  dividendYield: number;
  payoutRatio: number;
}

export interface StockPrediction {
  symbol: string;
  date: string;
  predictedPrice: number;
  lowerBound: number;
  upperBound: number;
  confidenceInterval: number;
}

export interface NewsItem {
  id: string;
  title: string;
  summary: string;
  source: string;
  url: string;
  imageUrl?: string;
  publishedAt: Date;
  relatedSymbols?: string[];
  sentiment: number;
}
