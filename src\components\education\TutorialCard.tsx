
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, BookOpen, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TutorialCardProps {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  completed?: boolean;
  onStart: (id: string) => void;
  className?: string;
}

export const TutorialCard = React.memo(function TutorialCard({
  id,
  title,
  description,
  duration,
  difficulty,
  completed = false,
  onStart,
  className
}: TutorialCardProps) {
  const difficultyColors = {
    Beginner: 'bg-green-100 text-green-800',
    Intermediate: 'bg-yellow-100 text-yellow-800',
    Advanced: 'bg-red-100 text-red-800'
  };

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              {title}
              {completed && <CheckCircle className="h-4 w-4 text-green-600" />}
            </CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <Badge className={difficultyColors[difficulty]} variant="secondary">
            {difficulty}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            {duration}
          </div>
          <Button 
            onClick={() => onStart(id)}
            variant={completed ? "outline" : "default"}
            size="sm"
          >
            {completed ? "Review" : "Start Tutorial"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});
