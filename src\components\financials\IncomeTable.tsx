
import { formatLargeCurrency } from '@/utils/formatters';
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '@/components/ui/table';
import type { FinancialData } from '@/types/stock';

interface IncomeTableProps {
  financialData: FinancialData[];
}

export function IncomeTable({ financialData }: IncomeTableProps) {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Period</TableHead>
            <TableHead className="text-right">Revenue</TableHead>
            <TableHead className="text-right">Net Income</TableHead>
            <TableHead className="text-right">EBITDA</TableHead>
            <TableHead className="text-right">EPS</TableHead>
            <TableHead className="text-right">Gross Margin</TableHead>
            <TableHead className="text-right">Net Margin</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {financialData.map((data) => (
            <TableRow key={data.period}>
              <TableCell>{data.period}</TableCell>
              <TableCell className="text-right">{formatLargeCurrency(data.revenue)}</TableCell>
              <TableCell className="text-right">{formatLargeCurrency(data.netIncome)}</TableCell>
              <TableCell className="text-right">{formatLargeCurrency(data.ebitda)}</TableCell>
              <TableCell className="text-right">${data.eps.toFixed(2)}</TableCell>
              <TableCell className="text-right">{data.grossMargin.toFixed(1)}%</TableCell>
              <TableCell className="text-right">{data.netMargin.toFixed(1)}%</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
