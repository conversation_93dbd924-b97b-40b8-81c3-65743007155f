
import React from 'react';
import { 
  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, 
  Legend, ResponsiveContainer, ReferenceLine, Area, ComposedChart
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StockPrediction, formatCurrency } from '@/utils/stockApi';

interface PredictionChartProps {
  predictions: StockPrediction[];
  historicalPrices: { date: string; price: number }[];
  symbol: string;
  className?: string;
}

export function PredictionChart({ 
  predictions, 
  historicalPrices, 
  symbol, 
  className 
}: PredictionChartProps) {
  // Combine historical and prediction data
  const chartData = [
    ...historicalPrices.map(item => ({
      date: item.date,
      price: item.price,
      isHistorical: true
    })),
    ...predictions.map(item => ({
      date: item.date,
      predictedPrice: item.predictedPrice,
      lowerBound: item.lowerBound,
      upperBound: item.upperBound,
      isHistorical: false
    }))
  ];

  // Find the date where predictions start
  const predictionStart = predictions[0]?.date;
  
  // Custom tooltip to show appropriate data based on historical vs prediction
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-card p-3 border border-border rounded-md shadow">
          <p className="font-medium">{label}</p>
          {data.isHistorical ? (
            <p>Price: {formatCurrency(data.price)}</p>
          ) : (
            <>
              <p>Predicted: {formatCurrency(data.predictedPrice)}</p>
              <p className="text-xs text-muted-foreground">
                Range: {formatCurrency(data.lowerBound)} - {formatCurrency(data.upperBound)}
              </p>
            </>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex justify-between">
          <span>{symbol} Price Prediction</span>
          <span className="text-sm text-muted-foreground">80% Confidence Interval</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart
              data={chartData}
              margin={{ top: 10, right: 30, left: 10, bottom: 10 }}
            >
              <defs>
                <linearGradient id="colorUpper" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--border))" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 10 }}
                tickMargin={10} 
              />
              <YAxis
                domain={['auto', 'auto']}
                tick={{ fontSize: 10 }}
                tickMargin={10}
                tickFormatter={(value) => formatCurrency(value)}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {/* Historical price line */}
              <Line
                type="monotone"
                dataKey="price"
                name="Historical Price"
                stroke="hsl(var(--success))"
                strokeWidth={2}
                dot={false}
                connectNulls
              />

              {/* Predicted price line */}
              <Line
                type="monotone"
                dataKey="predictedPrice"
                name="Predicted Price"
                stroke="hsl(var(--primary))"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
                connectNulls
              />

              {/* Confidence Interval */}
              <Area
                type="monotone"
                dataKey="upperBound"
                stroke="transparent"
                fill="transparent"
                connectNulls
              />
              <Area
                type="monotone"
                dataKey="lowerBound"
                stroke="transparent"
                fill="url(#colorUpper)"
                fillOpacity={0.6}
                connectNulls
              />

              {/* Reference line for prediction start */}
              {predictionStart && (
                <ReferenceLine
                  x={predictionStart}
                  stroke="hsl(var(--muted-foreground))"
                  strokeDasharray="3 3"
                  label={{ value: "Prediction Start", position: "insideTopRight", fontSize: 10 }}
                />
              )}
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
