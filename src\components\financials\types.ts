
import { FinancialData, CashFlowData, BalanceSheetData } from '@/types/stock';

export interface MetricCardProps {
  title: string;
  value: string;
  trend?: string;
  status?: string;
}

export interface IncomeTabProps {
  financialData: FinancialData[];
}

export interface BalanceSheetTabProps {
  balanceSheetData: BalanceSheetData[];
}

export interface CashFlowTabProps {
  cashFlowData: CashFlowData[];
}
