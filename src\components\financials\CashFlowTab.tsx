
import { formatLargeCurrency } from '@/utils/formatters';
import { MetricCard } from './MetricCard';
import type { CashFlowTabProps } from './types';

export function CashFlowTab({ cashFlowData }: CashFlowTabProps) {
  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-2 px-4">Period</th>
              <th className="text-right py-2 px-4">Operating CF</th>
              <th className="text-right py-2 px-4">Investing CF</th>
              <th className="text-right py-2 px-4">Financing CF</th>
              <th className="text-right py-2 px-4">Free Cash Flow</th>
              <th className="text-right py-2 px-4">CapEx</th>
            </tr>
          </thead>
          <tbody>
            {cashFlowData.map((data) => (
              <tr key={data.period} className="border-b">
                <td className="py-3 px-4">{data.period}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.operatingCashFlow)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.investingCashFlow)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.financingCashFlow)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.freeCashFlow)}</td>
                <td className="text-right py-3 px-4">{formatLargeCurrency(data.capex)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <MetricCard
          title="FCF Yield"
          value="3.4%"
          trend="Strong"
          status="text-success"
        />
        <MetricCard
          title="FCF/Revenue"
          value="25.3%"
          trend="Industry Leading"
          status="text-success"
        />
        <MetricCard
          title="FCF Growth"
          value="-10.7%"
          trend="YoY Decline"
          status="text-danger"
        />
      </div>
    </div>
  );
}
