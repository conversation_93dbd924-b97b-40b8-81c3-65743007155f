
import { Separator } from "@/components/ui/separator";
import { ScoreIndicator } from "@/components/prospective/ScoreIndicator";
import { ChartBar, ChartLine, DollarSign } from "lucide-react";
import type { Commodity } from "@/types/commodities";

interface MetricsSectionProps {
  commodity: Commodity;
}

export function MetricsSection({ commodity }: MetricsSectionProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <span>YTD Return</span>
        </div>
        <span className={`font-medium ${commodity.yearToDateReturn >= 0 ? "text-success" : "text-danger"}`}>
          {commodity.yearToDateReturn >= 0 ? "+" : ""}
          {commodity.yearToDateReturn.toFixed(2)}%
        </span>
      </div>
      <Separator />

      <div className="flex items-center justify-between">
        <span>52 Week Range</span>
        <span className="font-medium">
          ${commodity.yearLow.toFixed(2)} - ${commodity.yearHigh.toFixed(2)}
        </span>
      </div>
      <Separator />

      <div className="flex items-center justify-between">
        <span>Volume</span>
        <span className="font-medium">{commodity.volume.toLocaleString()}</span>
      </div>
      <Separator />

      <div className="flex items-center justify-between">
        <span>Open Interest</span>
        <span className="font-medium">{commodity.openInterest.toLocaleString()}</span>
      </div>
      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ChartBar className="h-4 w-4 text-muted-foreground" />
          <span>Inflation Hedge</span>
        </div>
        <div className="w-24">
          <ScoreIndicator
            score={commodity.inflationHedgeScore}
            label=""
            showLabel={false}
            size="sm"
          />
        </div>
      </div>
      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ChartLine className="h-4 w-4 text-muted-foreground" />
          <span>Recession Resistance</span>
        </div>
        <div className="w-24">
          <ScoreIndicator
            score={commodity.recessionResistanceScore}
            label=""
            showLabel={false}
            size="sm"
          />
        </div>
      </div>
      <Separator />

      <div className="flex items-center justify-between">
        <span>Market Correlation</span>
        <span className="font-medium">{commodity.correlationWithSP500.toFixed(2)}</span>
      </div>
      
      {commodity.sustainabilityScore && (
        <>
          <Separator />
          <div className="flex items-center justify-between">
            <span>ESG Score</span>
            <div className="w-24">
              <ScoreIndicator
                score={commodity.sustainabilityScore}
                label=""
                showLabel={false}
                size="sm"
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
}
