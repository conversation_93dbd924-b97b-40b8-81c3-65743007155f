
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { TrendingUp, TrendingDown, Minus, CheckCircle, X } from 'lucide-react';

const chartData = [
  { time: '9:00', price: 100, volume: 1200 },
  { time: '9:30', price: 102, volume: 1800 },
  { time: '10:00', price: 98, volume: 2200 },
  { time: '10:30', price: 104, volume: 2800 },
  { time: '11:00', price: 107, volume: 3200 },
  { time: '11:30', price: 105, volume: 1900 },
  { time: '12:00', price: 108, volume: 2600 },
  { time: '12:30', price: 112, volume: 3800 },
  { time: '13:00', price: 115, volume: 4200 },
  { time: '13:30', price: 113, volume: 2100 }
];

const questions = [
  {
    id: 1,
    question: "What is the overall trend in this chart?",
    options: ["Uptrend", "Downtrend", "Sideways"],
    correct: 0,
    explanation: "The price moves from $100 to $113, showing a clear upward trend despite some fluctuations."
  },
  {
    id: 2,
    question: "What happened at 10:00 AM?",
    options: ["Price breakout", "Price pullback", "Volume spike"],
    correct: 1,
    explanation: "At 10:00 AM, the price pulled back from $102 to $98, which is a normal pullback in an uptrend."
  },
  {
    id: 3,
    question: "When was the highest volume recorded?",
    options: ["11:00 AM", "12:30 PM", "1:00 PM"],
    correct: 2,
    explanation: "The highest volume of 4,200 was recorded at 1:00 PM, coinciding with the price reaching $115."
  }
];

export const InteractiveChartReading = React.memo(function InteractiveChartReading() {
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: number }>({});
  const [showResults, setShowResults] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);

  const handleAnswerSelect = (questionId: number, answerIndex: number) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answerIndex
    }));
  };

  const checkAnswers = () => {
    setShowResults(true);
  };

  const resetQuiz = () => {
    setSelectedAnswers({});
    setShowResults(false);
    setCurrentQuestion(0);
  };

  const getScore = () => {
    let correct = 0;
    questions.forEach(q => {
      if (selectedAnswers[q.id] === q.correct) {
        correct++;
      }
    });
    return correct;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-semibold">{`Time: ${label}`}</p>
          <p className="text-blue-600">{`Price: $${data.price}`}</p>
          <p className="text-purple-600">{`Volume: ${data.volume.toLocaleString()}`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Interactive Chart Reading Exercise</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Chart */}
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="time" 
                stroke="#666"
                fontSize={12}
              />
              <YAxis 
                stroke="#666"
                fontSize={12}
                domain={['dataMin - 2', 'dataMax + 2']}
              />
              <Tooltip content={<CustomTooltip />} />
              
              {/* Support and Resistance Lines */}
              <ReferenceLine y={100} stroke="#ef4444" strokeDasharray="5 5" label="Support" />
              <ReferenceLine y={115} stroke="#10b981" strokeDasharray="5 5" label="Resistance" />
              
              <Line 
                type="monotone" 
                dataKey="price" 
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Chart Analysis Points */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="font-semibold text-green-800">Resistance at $115</span>
            </div>
            <p className="text-sm text-green-700">
              Price struggled to break above this level
            </p>
          </div>
          
          <div className="p-3 bg-red-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingDown className="h-4 w-4 text-red-600" />
              <span className="font-semibold text-red-800">Support at $100</span>
            </div>
            <p className="text-sm text-red-700">
              Price bounced up from this level
            </p>
          </div>
          
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Minus className="h-4 w-4 text-blue-600" />
              <span className="font-semibold text-blue-800">Volume Pattern</span>
            </div>
            <p className="text-sm text-blue-700">
              Volume increased with price rises
            </p>
          </div>
        </div>

        {/* Questions */}
        <div className="space-y-4">
          <h4 className="font-semibold">Test Your Chart Reading Skills:</h4>
          
          {questions.map((question) => (
            <Card key={question.id} className="p-4">
              <div className="space-y-3">
                <h5 className="font-medium">{question.question}</h5>
                
                <div className="space-y-2">
                  {question.options.map((option, index) => (
                    <label key={index} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        name={`question-${question.id}`}
                        value={index}
                        checked={selectedAnswers[question.id] === index}
                        onChange={() => handleAnswerSelect(question.id, index)}
                        className="text-primary"
                        disabled={showResults}
                      />
                      <span className={`text-sm ${
                        showResults && index === question.correct 
                          ? 'text-green-600 font-semibold' 
                          : showResults && selectedAnswers[question.id] === index && index !== question.correct
                            ? 'text-red-600'
                            : ''
                      }`}>
                        {option}
                        {showResults && index === question.correct && (
                          <CheckCircle className="inline h-4 w-4 ml-2 text-green-600" />
                        )}
                        {showResults && selectedAnswers[question.id] === index && index !== question.correct && (
                          <X className="inline h-4 w-4 ml-2 text-red-600" />
                        )}
                      </span>
                    </label>
                  ))}
                </div>

                {showResults && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Explanation:</strong> {question.explanation}
                    </p>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>

        {/* Results */}
        {showResults && (
          <Card className="p-4 bg-green-50">
            <div className="text-center">
              <h4 className="font-semibold text-green-800 mb-2">
                Quiz Complete! Score: {getScore()}/{questions.length}
              </h4>
              <Badge 
                variant={getScore() === questions.length ? 'default' : getScore() >= questions.length * 0.7 ? 'secondary' : 'destructive'}
              >
                {getScore() === questions.length ? 'Perfect!' : getScore() >= questions.length * 0.7 ? 'Good Job!' : 'Keep Practicing!'}
              </Badge>
            </div>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center gap-2">
          {!showResults ? (
            <Button 
              onClick={checkAnswers}
              disabled={Object.keys(selectedAnswers).length !== questions.length}
            >
              Check Answers
            </Button>
          ) : (
            <Button onClick={resetQuiz} variant="outline">
              Try Again
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
});
