
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  value: string | number;
  trend?: number;
  trendLabel?: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

export function StatsCard({ 
  title, 
  value, 
  trend, 
  trendLabel,
  description, 
  icon,
  className 
}: StatsCardProps) {
  const isTrendPositive = trend !== undefined && trend >= 0;

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            
            {trend !== undefined && (
              <div className={cn(
                "text-sm flex items-center gap-1",
                isTrendPositive ? "text-success" : "text-danger"
              )}>
                {isTrendPositive ? '↑' : '↓'} 
                {Math.abs(trend).toFixed(2)}%
                {trendLabel && <span className="text-muted-foreground ml-1">({trendLabel})</span>}
              </div>
            )}
            
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
          
          {icon && (
            <div className="p-2 rounded-md bg-primary/10 text-primary">
              {icon}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
