
import React from 'react';
import { <PERSON>, Bell, User, DollarSign, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ThemeToggle } from '@/components/theme/ThemeToggle';
import { Link } from 'react-router-dom';

interface NavbarProps {
  className?: string;
  onSearch?: (term: string) => void;
  children?: React.ReactNode;
}

export function Navbar({ className, onSearch, children }: NavbarProps) {
  const [searchTerm, setSearchTerm] = React.useState('');
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && searchTerm.trim()) {
      onSearch(searchTerm);
    }
  };
  
  return (
    <header className={cn("bg-background/95 backdrop-blur-sm sticky top-0 z-30 border-b", className)}>
      <div className="container flex items-center justify-between h-16 px-4">
        <div className="flex items-center gap-2 lg:gap-4">
          <div className="flex items-center">
            <DollarSign className="h-6 w-6 text-primary mr-2" />
            <Link to="/" className="text-lg font-semibold tracking-tight lg:text-xl hover:text-primary transition-colors">
              OptiVestor
            </Link>
          </div>
          
          <form onSubmit={handleSearch} className="relative hidden md:flex items-center h-9 rounded-md px-3 text-muted-foreground focus-within:text-foreground bg-muted/50">
            <Search className="h-4 w-4 mr-2" />
            <Input 
              type="search" 
              placeholder="Search stocks, indices..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-9 w-[200px] lg:w-[280px] bg-transparent border-none px-0 py-0 shadow-none focus-visible:ring-0 placeholder:text-muted-foreground"
            />
          </form>
        </div>
        
        <div className="flex items-center gap-4">
          <ThemeToggle />
          
          <Button 
            variant="ghost" 
            size="icon" 
            className="relative h-9 w-9"
          >
            <Bell className="h-5 w-5" />
            <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-primary animate-pulse" />
          </Button>

          <Link to="/settings">
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-9 w-9"
            >
              <Settings className="h-5 w-5" />
            </Button>
          </Link>
          
          <Avatar className="h-9 w-9 transition-transform duration-200 hover:scale-105">
            <AvatarFallback className="bg-primary/10 text-primary">
              <User className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>

          {children}
        </div>
      </div>
    </header>
  );
}

export type { NavbarProps };
