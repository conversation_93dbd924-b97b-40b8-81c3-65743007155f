
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const CandlestickPatternsChart = React.memo(function CandlestickPatternsChart() {
  const patterns = [
    {
      name: 'Bullish Candle',
      type: 'bullish',
      open: 100,
      close: 120,
      high: 125,
      low: 95,
      description: 'Close > Open = Buying pressure'
    },
    {
      name: 'Bearish Candle',
      type: 'bearish',
      open: 120,
      close: 100,
      high: 125,
      low: 95,
      description: 'Close < Open = Selling pressure'
    },
    {
      name: 'Doji',
      type: 'neutral',
      open: 110,
      close: 110,
      high: 125,
      low: 95,
      description: 'Open = Close = Indecision'
    },
    {
      name: 'Hammer',
      type: 'bullish',
      open: 115,
      close: 120,
      high: 122,
      low: 95,
      description: 'Long lower wick = Bullish reversal'
    }
  ];

  const CandlestickSVG = ({ pattern, width = 80, height = 150 }: { pattern: any; width?: number; height?: number }) => {
    const { open, close, high, low, type } = pattern;
    const scale = height / (high - low + 10);
    const bodyTop = Math.min(open, close);
    const bodyBottom = Math.max(open, close);
    const bodyHeight = Math.abs(close - open);
    
    const isBullish = close > open;
    const isDoji = close === open;
    
    const wickColor = '#666';
    const bullishColor = '#10b981';
    const bearishColor = '#ef4444';
    const neutralColor = '#6b7280';
    
    const candleColor = isDoji ? neutralColor : isBullish ? bullishColor : bearishColor;

    return (
      <svg width={width} height={height} className="mx-auto">
        {/* High wick */}
        <line
          x1={width / 2}
          y1={10}
          x2={width / 2}
          y2={10 + (high - Math.max(open, close)) * scale}
          stroke={wickColor}
          strokeWidth="2"
        />
        
        {/* Low wick */}
        <line
          x1={width / 2}
          y1={height - 10 - (Math.min(open, close) - low) * scale}
          x2={width / 2}
          y2={height - 10}
          stroke={wickColor}
          strokeWidth="2"
        />
        
        {/* Body */}
        <rect
          x={width / 2 - 15}
          y={10 + (high - Math.max(open, close)) * scale}
          width="30"
          height={isDoji ? 2 : bodyHeight * scale}
          fill={candleColor}
          stroke={candleColor}
          strokeWidth="1"
        />
        
        {/* Price labels */}
        <text x={width + 5} y={15} fontSize="10" fill="#666">
          H: {high}
        </text>
        <text x={width + 5} y={height - 5} fontSize="10" fill="#666">
          L: {low}
        </text>
        <text x={5} y={15 + (high - open) * scale} fontSize="10" fill="#666">
          O: {open}
        </text>
        <text x={5} y={15 + (high - close) * scale} fontSize="10" fill="#666">
          C: {close}
        </text>
      </svg>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Candlestick Pattern Examples</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {patterns.map((pattern, index) => (
            <div key={index} className="text-center space-y-3">
              <CandlestickSVG pattern={pattern} />
              <div>
                <h4 className="font-semibold">{pattern.name}</h4>
                <Badge 
                  variant={pattern.type === 'bullish' ? 'default' : pattern.type === 'bearish' ? 'destructive' : 'secondary'}
                  className="mb-2"
                >
                  {pattern.type}
                </Badge>
                <p className="text-xs text-muted-foreground">{pattern.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold mb-2">Key Elements:</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Body:</strong> Rectangle showing open and close prices
            </div>
            <div>
              <strong>Wicks:</strong> Lines showing high and low prices
            </div>
            <div>
              <strong>Color:</strong> Green for bullish, red for bearish
            </div>
            <div>
              <strong>Size:</strong> Larger bodies indicate stronger momentum
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
