
import { 
  <PERSON><PERSON>hart, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, 
  <PERSON>ltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface BalanceSheetChartProps {
  data: {
    period: string;
    assets: number;
    liabilities: number;
    equity: number;
  }[];
}

export const BalanceSheetChart = ({ data }: BalanceSheetChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Balance Sheet (Billions)</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="period" />
            <YAxis />
            <Tooltip 
              formatter={(value: number) => [`$${value.toFixed(2)}B`, '']}
            />
            <Legend />
            <Bar 
              dataKey="assets" 
              fill="hsl(var(--primary))" 
              name="Total Assets"
              barSize={60} 
            />
            <Bar 
              dataKey="liabilities" 
              fill="hsl(var(--muted))" 
              name="Total Liabilities"
              barSize={60} 
            />
            <Bar 
              dataKey="equity" 
              fill="hsl(var(--success))" 
              name="Total Equity"
              barSize={60} 
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
