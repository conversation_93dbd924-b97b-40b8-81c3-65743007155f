
import React from 'react';
import { 
  ChevronLeft, ChevronRight, LineChart, Pie<PERSON>hart, BarChart, 
  DollarSign, Bell, Settings, Search, Home, BookOpen, Star, GraduationCap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Link, useLocation } from 'react-router-dom';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  className?: string;
}

interface NavItem {
  title: string;
  icon: React.ElementType;
  href: string;
  subItems?: { title: string; href: string; }[];
}

export function Sidebar({ isCollapsed, onToggle, className }: SidebarProps) {
  const location = useLocation();
  
  const navItems = [
    {
      title: 'Dashboard',
      icon: Home,
      href: '/',
    },
    {
      title: 'Market Analysis',
      icon: Search,
      href: '/stocks',
      subItems: [
        { title: 'Stock Explorer', href: '/stocks' },
        { title: 'Global Markets', href: '/global' },
        { title: 'Prospective Stocks', href: '/prospective-stocks' },
        { title: 'Commodities', href: '/commodities' },
      ]
    },
    {
      title: 'Performance',
      icon: BarChart,
      href: '/financial',
      subItems: [
        { title: 'Financial Analysis', href: '/financial' },
        { title: 'Portfolio Performance', href: '/portfolio' },
      ]
    },
    {
      title: 'Forecasting',
      icon: LineChart,
      href: '/predictions',
      subItems: [
        { title: 'Market Predictions', href: '/predictions' },
        { title: 'Prediction Algorithms', href: '/prediction-algorithms' },
        { title: 'Market Trends', href: '/markets' },
      ]
    },
    {
      title: 'Monitoring',
      icon: Star,
      href: '/watchlist',
      subItems: [
        { title: 'Watchlist', href: '/watchlist' },
        { title: 'Alerts', href: '/alerts' },
      ]
    },
    {
      title: 'Research',
      icon: BookOpen,
      href: '/news',
      subItems: [
        { title: 'News & Updates', href: '/news' },
        { title: 'Analysis Reports', href: '/analysis' },
      ]
    },
    {
      title: 'Education',
      icon: GraduationCap,
      href: '/education',
      subItems: [
        { title: 'Interactive Tutorials', href: '/education?tab=tutorials' },
        { title: 'Education Modules', href: '/education?tab=modules' },
        { title: 'Risk Guides', href: '/education?tab=risk-guides' },
        { title: 'Investment Strategies', href: '/education?tab=strategies' },
      ]
    },
    {
      title: 'Settings',
      icon: Settings,
      href: '/settings',
    }
  ];

  return (
    <aside className={cn(
      "bg-sidebar text-sidebar-foreground relative transition-all duration-300 ease-in-out flex flex-col border-r border-border",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      <div className="flex h-16 items-center justify-center border-b border-border">
        <h2 className={cn(
          "font-semibold tracking-tight transition-opacity duration-200",
          isCollapsed ? "opacity-0" : "opacity-100"
        )}>
          OptiVestor
        </h2>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className={cn(
            "absolute right-2 text-sidebar-foreground h-8 w-8",
            isCollapsed ? "right-2" : "right-4"
          )}
        >
          {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>
      
      <ScrollArea className="flex-1 py-4">
        <nav className="grid gap-1 px-2">
          {navItems.map((item, index) => {
            const isActive = location.pathname === item.href || 
              (item.subItems?.some(sub => location.pathname === sub.href));
            
            return (
              <div key={index}>
                <Link
                  to={item.href}
                  className={cn(
                    "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors hover:bg-secondary hover:text-primary",
                    isActive ? "bg-secondary text-primary" : "text-sidebar-foreground",
                    isCollapsed && "justify-center px-0"
                  )}
                >
                  <item.icon className={cn("h-5 w-5 shrink-0")} />
                  <span className={cn(
                    "text-sm font-medium transition-opacity duration-200",
                    isCollapsed ? "opacity-0 w-0" : "opacity-100"
                  )}>
                    {item.title}
                  </span>
                </Link>
                
                {!isCollapsed && item.subItems && (
                  <div className="ml-6 mt-1 grid gap-1">
                    {item.subItems.map((subItem, subIndex) => {
                      const isSubActive = location.pathname === subItem.href;
                      return (
                        <Link
                          key={subIndex}
                          to={subItem.href}
                          className={cn(
                            "flex items-center gap-2 rounded-md px-3 py-1.5 text-sm transition-colors hover:bg-secondary hover:text-primary",
                            isSubActive ? "text-primary" : "text-sidebar-foreground/80"
                          )}
                        >
                          {subItem.title}
                        </Link>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </nav>
      </ScrollArea>
      
      <div className="p-4 border-t border-border">
        <div className={cn(
          "transition-opacity duration-200 rounded-md bg-secondary/50 p-2 text-xs text-foreground",
          isCollapsed ? "opacity-0" : "opacity-100"
        )}>
          <p className="font-medium">Market Status</p>
          <p className="text-success">Markets are open</p>
          <p className="text-[10px]">Closes in 3h 45m</p>
        </div>
      </div>
    </aside>
  );
}
