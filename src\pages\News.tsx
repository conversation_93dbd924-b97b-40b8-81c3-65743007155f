
import React, { useState, useEffect } from 'react';
import { PageLayout } from '@/components/layout/PageLayout';
import { StockNews } from '@/components/news/StockNews';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { mockNews } from '@/utils/stockApi';
import { LineChart, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';

const News = () => {
  const [newsItems, setNewsItems] = useState(mockNews);
  
  // Categorize news by sentiment
  const positiveNews = newsItems.filter(item => item.sentiment > 0.3);
  const neutralNews = newsItems.filter(item => item.sentiment >= -0.3 && item.sentiment <= 0.3);
  const negativeNews = newsItems.filter(item => item.sentiment < -0.3);
  
  // Mock market sentiment data
  const marketSentiment = {
    overall: 0.25, // Range from -1 to 1
    stocks: {
      positive: 62,
      neutral: 25,
      negative: 13
    },
    topMentioned: [
      { symbol: 'AAPL', count: 24, sentiment: 0.45 },
      { symbol: 'NVDA', count: 18, sentiment: 0.72 },
      { symbol: 'TSLA', count: 15, sentiment: -0.22 },
      { symbol: 'MSFT', count: 12, sentiment: 0.38 },
      { symbol: 'AMZN', count: 10, sentiment: 0.15 }
    ],
    trending: [
      { topic: 'AI Technology', sentiment: 0.68 },
      { topic: 'Interest Rates', sentiment: -0.35 },
      { topic: 'Earnings Season', sentiment: 0.12 },
      { topic: 'Tech Layoffs', sentiment: -0.58 },
      { topic: 'Supply Chain', sentiment: -0.15 }
    ]
  };
  
  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.3) return 'bg-success/20 text-success';
    if (sentiment < -0.3) return 'bg-danger/20 text-danger';
    return 'bg-secondary text-foreground';
  };
  
  const getSentimentIcon = (sentiment: number) => {
    if (sentiment > 0.3) return <TrendingUp className="h-4 w-4" />;
    if (sentiment < -0.3) return <TrendingDown className="h-4 w-4" />;
    return <LineChart className="h-4 w-4" />;
  };
  
  return (
    <PageLayout title="News & Sentiment Analysis">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main news content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="all">
            <TabsList className="w-full grid grid-cols-4">
              <TabsTrigger value="all">All News</TabsTrigger>
              <TabsTrigger value="positive">
                Positive 
                <Badge variant="secondary" className="ml-2 bg-success/20 text-success">
                  {positiveNews.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="neutral">
                Neutral
                <Badge variant="secondary" className="ml-2">
                  {neutralNews.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="negative">
                Negative
                <Badge variant="secondary" className="ml-2 bg-danger/20 text-danger">
                  {negativeNews.length}
                </Badge>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="mt-4">
              <StockNews news={newsItems} />
            </TabsContent>
            
            <TabsContent value="positive" className="mt-4">
              <StockNews news={positiveNews} />
            </TabsContent>
            
            <TabsContent value="neutral" className="mt-4">
              <StockNews news={neutralNews} />
            </TabsContent>
            
            <TabsContent value="negative" className="mt-4">
              <StockNews news={negativeNews} />
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Sentiment sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-primary" />
                Market Sentiment Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Overall Market Sentiment</h3>
                <div className="flex items-center">
                  <div className="relative w-full h-3 bg-secondary rounded-full">
                    <div 
                      className={`absolute top-0 bottom-0 left-1/2 rounded-full transition-all duration-300 ${
                        marketSentiment.overall > 0.3 ? 'bg-success' : 
                        marketSentiment.overall < -0.3 ? 'bg-danger' : 
                        'bg-warning'
                      }`}
                      style={{ 
                        width: `${Math.abs(marketSentiment.overall) * 100}%`, 
                        left: marketSentiment.overall < 0 
                          ? `calc(50% - ${Math.abs(marketSentiment.overall) * 100}%)`
                          : '50%'
                      }}
                    ></div>
                    <div className="absolute top-0 bottom-0 left-1/2 w-0.5 -ml-0.5 bg-muted-foreground"></div>
                  </div>
                </div>
                <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                  <span>Bearish</span>
                  <span>Neutral</span>
                  <span>Bullish</span>
                </div>
              </div>
              
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Sentiment Distribution</h3>
                <div className="flex">
                  <div 
                    className="h-6 bg-success rounded-l-full text-xs text-success-foreground flex items-center justify-center"
                    style={{ width: `${marketSentiment.stocks.positive}%` }}
                  >
                    {marketSentiment.stocks.positive}%
                  </div>
                  <div 
                    className="h-6 bg-secondary text-xs text-secondary-foreground flex items-center justify-center"
                    style={{ width: `${marketSentiment.stocks.neutral}%` }}
                  >
                    {marketSentiment.stocks.neutral}%
                  </div>
                  <div 
                    className="h-6 bg-danger rounded-r-full text-xs text-danger-foreground flex items-center justify-center"
                    style={{ width: `${marketSentiment.stocks.negative}%` }}
                  >
                    {marketSentiment.stocks.negative}%
                  </div>
                </div>
                <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                  <span>Positive</span>
                  <span>Neutral</span>
                  <span>Negative</span>
                </div>
              </div>
              
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-3">Most Mentioned Stocks</h3>
                <div className="space-y-2">
                  {marketSentiment.topMentioned.map(item => (
                    <div key={item.symbol} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Badge variant="outline" className="mr-2">{item.symbol}</Badge>
                        <span className="text-xs text-muted-foreground">{item.count} mentions</span>
                      </div>
                      <Badge className={getSentimentColor(item.sentiment)}>
                        {getSentimentIcon(item.sentiment)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-3">Trending Topics</h3>
                <div className="space-y-2">
                  {marketSentiment.trending.map(item => (
                    <div key={item.topic} className="flex items-center justify-between">
                      <span className="text-sm">{item.topic}</span>
                      <Badge className={getSentimentColor(item.sentiment)}>
                        {getSentimentIcon(item.sentiment)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>News Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Array.from(new Set(mockNews.map(item => item.source))).map(source => (
                  <div key={source} className="flex items-center justify-between">
                    <span>{source}</span>
                    <Badge variant="outline">
                      {mockNews.filter(item => item.source === source).length} articles
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
};

export default News;
