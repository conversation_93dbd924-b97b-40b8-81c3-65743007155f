
import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { PageLayout } from '@/components/layout/PageLayout';
import { StockCard } from '@/components/stocks/StockCard';
import { StockDetails } from '@/components/stocks/StockDetails';
import { PredictionChart } from '@/components/predictions/PredictionChart';
import { FinancialMetrics } from '@/components/financials/FinancialMetrics';
import { StockNews } from '@/components/news/StockNews';
import { 
  useStockData, mockStocks, mockNews, getPredictions, 
  getFinancialData, getBalanceSheetData, getCashFlowData,
  generatePriceHistory
} from '@/utils/stockApi';

const Stocks = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const urlSymbol = searchParams.get('symbol');
  
  const stocks = useStockData();
  const [selectedStockSymbol, setSelectedStockSymbol] = useState(urlSymbol || 'AAPL');
  
  // Get the selected stock details
  const selectedStock = stocks.find(stock => stock.symbol === selectedStockSymbol) || stocks[0];
  
  // Get financial data for the selected stock
  const financialData = getFinancialData(selectedStock.symbol);
  const balanceSheetData = getBalanceSheetData(selectedStock.symbol);
  const cashFlowData = getCashFlowData(selectedStock.symbol);
  
  // Generate historical price data for charts
  const daysOfHistory = 60;
  const historicalPrices = generatePriceHistory(daysOfHistory, selectedStock.price, 2)
    .map((price, index) => {
      const date = new Date();
      date.setDate(date.getDate() - (daysOfHistory - index));
      return {
        date: date.toISOString().slice(0, 7), // Format as YYYY-MM
        price
      };
    });
  
  // Get price predictions for the selected stock
  const predictions = getPredictions(selectedStock.symbol);
  
  // Filter news related to the selected stock
  const filteredNews = mockNews.filter(news => 
    !news.relatedSymbols || 
    news.relatedSymbols.includes(selectedStock.symbol)
  );
  
  // Handle stock search
  const handleSearch = (term: string) => {
    const foundStock = stocks.find(stock => 
      stock.symbol.toLowerCase() === term.toLowerCase() || 
      stock.name.toLowerCase().includes(term.toLowerCase())
    );
    
    if (foundStock) {
      setSelectedStockSymbol(foundStock.symbol);
      navigate(`/stocks?symbol=${foundStock.symbol}`);
    }
  };
  
  const stocksWithHistory = stocks.map(stock => {
    return {
      ...stock,
      priceHistory: generatePriceHistory(30, stock.price, 2)
    };
  });
  
  return (
    <PageLayout title="Stock Explorer" onSearch={handleSearch}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Stock List */}
        <div className="lg:col-span-1 space-y-4">
          <h2 className="text-xl font-semibold">Stock List</h2>
          <div className="space-y-4">
            {stocksWithHistory.map((stock) => (
              <StockCard 
                key={stock.symbol} 
                stock={stock} 
                priceHistory={stock.priceHistory}
                onClick={() => {
                  setSelectedStockSymbol(stock.symbol);
                  navigate(`/stocks?symbol=${stock.symbol}`);
                }}
                className={selectedStockSymbol === stock.symbol ? "ring-2 ring-primary" : ""}
              />
            ))}
          </div>
        </div>
        
        {/* Stock Details and Analysis */}
        <div className="lg:col-span-3 space-y-6">
          {/* Stock Overview */}
          <StockDetails stock={selectedStock} />
          
          {/* Price Prediction Chart */}
          <PredictionChart 
            predictions={predictions} 
            historicalPrices={historicalPrices}
            symbol={selectedStock.symbol}
          />
          
          {/* Financial Metrics */}
          <FinancialMetrics
            symbol={selectedStock.symbol}
            financialData={financialData}
            balanceSheetData={balanceSheetData}
            cashFlowData={cashFlowData}
          />
          
          {/* News */}
          <StockNews news={filteredNews.length > 0 ? filteredNews : mockNews.slice(0, 2)} />
        </div>
      </div>
    </PageLayout>
  );
};

export default Stocks;
