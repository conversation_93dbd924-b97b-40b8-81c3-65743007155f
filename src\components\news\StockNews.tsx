
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { NewsItem, formatDate } from '@/utils/stockApi';
import { cn } from '@/lib/utils';
import { BookOpen } from 'lucide-react';

interface StockNewsProps {
  news: NewsItem[];
  className?: string;
}

export function StockNews({ news, className }: StockNewsProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <div className="flex items-center">
          <BookOpen className="h-5 w-5 mr-2" />
          <CardTitle>Market News</CardTitle>
        </div>
        
        <Badge variant="outline" className="ml-auto">
          Live Updates
        </Badge>
      </CardHeader>
      <CardContent className="p-0">
        <div className="divide-y divide-border">
          {news.map((item) => (
            <div key={item.id} className="p-4 transition-colors hover:bg-muted/30">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-base">{item.title}</h4>
                <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                  {formatDate(item.publishedAt)}
                </span>
              </div>
              
              <p className="text-sm text-muted-foreground mb-3">{item.summary}</p>
              
              {item.imageUrl && (
                <div className="relative h-32 mb-3 overflow-hidden rounded-md">
                  <img 
                    src={item.imageUrl} 
                    alt={item.title}
                    className="absolute inset-0 h-full w-full object-cover" 
                  />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex gap-1">
                  {item.relatedSymbols?.map((symbol) => (
                    <Badge key={symbol} variant="outline" className="text-xs">
                      {symbol}
                    </Badge>
                  ))}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getSentimentBadgeVariant(item.sentiment)}>
                    {getSentimentLabel(item.sentiment)}
                  </Badge>
                  <span className="text-xs font-medium">{item.source}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export type { StockNewsProps };

// Helper functions for sentiment analysis
function getSentimentLabel(sentiment: number): string {
  if (sentiment > 0.5) return "Bullish";
  if (sentiment > 0.2) return "Positive";
  if (sentiment > -0.2) return "Neutral";
  if (sentiment > -0.5) return "Negative";
  return "Bearish";
}

function getSentimentBadgeVariant(sentiment: number): "default" | "secondary" | "destructive" | "outline" {
  if (sentiment > 0.5) return "default";
  if (sentiment > 0.2) return "secondary";
  if (sentiment > -0.2) return "outline";
  return "destructive";
}
