
import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { PageLayout } from '@/components/layout/PageLayout';
import { PredictionChart } from '@/components/predictions/PredictionChart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, 
  Legend, ResponsiveContainer, Area, AreaChart
} from 'recharts';
import { StockDetails } from '@/components/stocks/StockDetails';
import {
  useStockData, getPredictions, generatePriceHistory, formatCurrency
} from '@/utils/stockApi';

const Predictions = () => {
  const [searchParams] = useSearchParams();
  const urlSymbol = searchParams.get('symbol');
  
  const stocks = useStockData();
  const [selectedStockSymbol, setSelectedStockSymbol] = useState(urlSymbol || 'AAPL');
  const [timeframe, setTimeframe] = useState('3m');
  
  // Get the selected stock details
  const selectedStock = stocks.find(stock => stock.symbol === selectedStockSymbol) || stocks[0];
  
  // Generate historical price data for charts
  const daysOfHistory = 90; // 3 months
  const historicalPrices = generatePriceHistory(daysOfHistory, selectedStock.price, 2)
    .map((price, index) => {
      const date = new Date();
      date.setDate(date.getDate() - (daysOfHistory - index));
      return {
        date: date.toISOString().slice(0, 7), // Format as YYYY-MM
        price
      };
    });
  
  // Get price predictions for the selected stock
  const predictions = getPredictions(selectedStock.symbol);

  // Handle stock search
  const handleSearch = (term: string) => {
    const foundStock = stocks.find(stock => 
      stock.symbol.toLowerCase() === term.toLowerCase() || 
      stock.name.toLowerCase().includes(term.toLowerCase())
    );
    
    if (foundStock) {
      setSelectedStockSymbol(foundStock.symbol);
    }
  };
  
  // Mock correlation data
  const correlationData = [
    { factor: 'S&P 500', correlation: 0.85 },
    { factor: 'NASDAQ', correlation: 0.89 },
    { factor: '10Y Treasury', correlation: -0.54 },
    { factor: 'USD Index', correlation: -0.42 },
    { factor: 'Gold', correlation: -0.23 },
    { factor: 'Oil', correlation: 0.15 }
  ];
  
  // Mock risk metrics
  const riskMetrics = [
    { name: 'Volatility (Annualized)', value: '28.4%', description: 'Stock price fluctuation over time' },
    { name: 'Beta', value: '1.28', description: 'Sensitivity to market movements' },
    { name: 'Sharpe Ratio', value: '0.73', description: 'Risk-adjusted return' },
    { name: 'Maximum Drawdown', value: '-34.2%', description: 'Largest peak-to-trough decline' },
    { name: 'Value at Risk (95%)', value: '-3.7%', description: 'Potential daily loss with 95% confidence' },
  ];
  
  // Mock valuation sensitivity data
  const valuationSensitivity = [
    { growth: 5, pe: 20, price: selectedStock.price * 0.75 },
    { growth: 5, pe: 25, price: selectedStock.price * 0.85 },
    { growth: 5, pe: 30, price: selectedStock.price * 0.95 },
    { growth: 10, pe: 20, price: selectedStock.price * 0.9 },
    { growth: 10, pe: 25, price: selectedStock.price * 1.0 },
    { growth: 10, pe: 30, price: selectedStock.price * 1.1 },
    { growth: 15, pe: 20, price: selectedStock.price * 1.05 },
    { growth: 15, pe: 25, price: selectedStock.price * 1.15 },
    { growth: 15, pe: 30, price: selectedStock.price * 1.25 },
  ];

  // Scenario analysis data
  const scenarioData = [
    {
      name: 'Worst Case',
      prediction: selectedStock.price * 0.75,
      probability: '20%',
      description: 'Economic slowdown, decreased consumer spending',
    },
    {
      name: 'Base Case',
      prediction: selectedStock.price * 1.15,
      probability: '60%',
      description: 'Continued stable growth in line with sector',
    },
    {
      name: 'Best Case',
      prediction: selectedStock.price * 1.45,
      probability: '20%',
      description: 'Strong market performance, increased margins',
    },
  ];
  
  return (
    <PageLayout title="AI Price Predictions" onSearch={handleSearch}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold">
          {selectedStock.name} ({selectedStock.symbol}) Price Predictions
        </h2>
        <p className="text-muted-foreground">
          AI-powered stock price forecasts with confidence intervals and analysis
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        {/* Stock Overview */}
        <StockDetails stock={selectedStock} />
        
        {/* Price Prediction Chart */}
        <PredictionChart 
          predictions={predictions} 
          historicalPrices={historicalPrices}
          symbol={selectedStock.symbol}
        />
        
        {/* Prediction Analysis */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">12-Month Target Prices</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Lower Bound</span>
                  <Badge variant="outline" className="text-danger">
                    {formatCurrency(predictions[predictions.length - 1].lowerBound)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Expected Target</span>
                  <Badge variant="outline" className="bg-primary text-white">
                    {formatCurrency(predictions[predictions.length - 1].predictedPrice)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Upper Bound</span>
                  <Badge variant="outline" className="text-success">
                    {formatCurrency(predictions[predictions.length - 1].upperBound)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Potential Return</span>
                  <Badge>
                    {((predictions[predictions.length - 1].predictedPrice / selectedStock.price - 1) * 100).toFixed(2)}%
                  </Badge>
                </div>
                <div className="pt-2 mt-2 border-t text-xs text-muted-foreground">
                  <p>AI model confidence: 80%</p>
                  <p>Based on technical & fundamental analysis</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Price Correlation Factors</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                {correlationData.map((item) => (
                  <div key={item.factor}>
                    <div className="flex justify-between text-sm mb-1">
                      <span>{item.factor}</span>
                      <span className={`font-medium ${
                        item.correlation > 0.5 ? 'text-success' : 
                        item.correlation < -0.5 ? 'text-danger' : ''
                      }`}>
                        {item.correlation.toFixed(2)}
                      </span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-1.5">
                      <div 
                        className={`h-1.5 rounded-full ${
                          item.correlation > 0 ? 'bg-success' : 'bg-danger'
                        }`}
                        style={{ width: `${Math.abs(item.correlation) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
                <div className="pt-2 mt-2 border-t text-xs text-muted-foreground">
                  <p>Correlation range: -1 (inverse) to +1 (direct)</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Scenario Analysis</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-4">
                {scenarioData.map((scenario) => (
                  <div key={scenario.name} className="p-3 border rounded-md">
                    <div className="flex justify-between items-center mb-1">
                      <h3 className="font-medium text-sm">{scenario.name}</h3>
                      <Badge variant={
                        scenario.name === 'Worst Case' ? 'destructive' : 
                        scenario.name === 'Best Case' ? 'default' : 'secondary'
                      }>
                        {scenario.probability}
                      </Badge>
                    </div>
                    <p className="text-lg font-bold mb-1">{formatCurrency(scenario.prediction)}</p>
                    <p className="text-xs text-muted-foreground">{scenario.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Advanced Analysis Tabs */}
        <Tabs defaultValue="sensitivity">
          <TabsList className="w-full grid grid-cols-2">
            <TabsTrigger value="sensitivity">Valuation Sensitivity</TabsTrigger>
            <TabsTrigger value="risk">Risk Metrics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="sensitivity">
            <Card>
              <CardHeader>
                <CardTitle>Valuation Sensitivity Analysis</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Stock price sensitivity to earnings growth and PE multiple
                </p>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={valuationSensitivity}
                    margin={{ top: 10, right: 30, left: 10, bottom: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis 
                      dataKey="growth"
                      label={{ value: 'Earnings Growth (%)', position: 'insideBottom', offset: -10 }}
                    />
                    <YAxis 
                      domain={['dataMin - 20', 'dataMax + 20']}
                      label={{ value: 'Stock Price ($)', angle: -90, position: 'insideLeft' }}
                      tickFormatter={(value) => `$${value}`}
                    />
                    <Tooltip 
                      formatter={(value: number) => [`$${value.toFixed(2)}`, 'Price']}
                      labelFormatter={(value) => `Growth: ${value}%, PE: ${
                        valuationSensitivity.find(d => d.growth === value)?.pe
                      }`}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="price" 
                      name="Target Price" 
                      stroke="hsl(var(--primary))" 
                      fill="hsl(var(--primary-foreground))" 
                      fillOpacity={0.2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="risk">
            <Card>
              <CardHeader>
                <CardTitle>Risk Assessment Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {riskMetrics.map((metric) => (
                    <div key={metric.name} className="p-4 border rounded-md">
                      <h3 className="font-medium mb-1">{metric.name}</h3>
                      <p className="text-2xl font-bold mb-2">{metric.value}</p>
                      <p className="text-xs text-muted-foreground">{metric.description}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
};

export default Predictions;
