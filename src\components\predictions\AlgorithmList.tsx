
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

const algorithms = [
  {
    name: 'LSTM Networks',
    type: 'Deep Learning',
    timeframes: ['3-6 months', '6-12 months', '12-24 months'],
    accuracy: 'High',
    description: 'Specialized recurrent neural networks for capturing long-term dependencies in time series data.',
  },
  {
    name: 'Transformer Models',
    type: 'Deep Learning',
    timeframes: ['6-12 months', '12-24 months'],
    accuracy: 'Very High',
    description: 'Attention-based architecture for identifying complex patterns across different timeframes.',
  },
  {
    name: 'Random Forest Ensembles',
    type: 'Machine Learning',
    timeframes: ['3-12 months'],
    accuracy: 'High',
    description: 'Combines multiple decision trees to reduce overfitting while handling non-linear relationships.',
  },
  {
    name: 'Gradient Boosting',
    type: 'Machine Learning',
    timeframes: ['1-6 months', '6-12 months'],
    accuracy: 'Very High',
    description: 'Sequential ensemble methods that build models iteratively for robust predictions.',
  },
  {
    name: 'Deep Reinforcement Learning',
    type: 'Deep Learning',
    timeframes: ['3-12 months', '12-24 months'],
    accuracy: 'High',
    description: 'Uses agents that learn optimal trading strategies through market environment interaction.',
  },
  {
    name: 'Temporal CNN',
    type: 'Deep Learning',
    timeframes: ['1-6 months', '6-12 months'],
    accuracy: 'High',
    description: 'Specialized CNNs for sequence modeling that capture hierarchical patterns in price movements.',
  },
  {
    name: 'Bayesian Neural Networks',
    type: 'Deep Learning',
    timeframes: ['6-24 months'],
    accuracy: 'High',
    description: 'Incorporates uncertainty estimation in predictions for longer-term forecasts.',
  },
  {
    name: 'Ensemble Methods',
    type: 'Hybrid',
    timeframes: ['3-24 months'],
    accuracy: 'Very High',
    description: 'Combines multiple algorithms with domain-specific feature engineering.',
  },
  {
    name: 'Attention-LSTM',
    type: 'Deep Learning',
    timeframes: ['3-12 months', '12-24 months'],
    accuracy: 'Very High',
    description: 'Hybrid models combining LSTM capabilities with attention mechanisms.',
  },
  {
    name: 'Graph Neural Networks',
    type: 'Deep Learning',
    timeframes: ['6-24 months'],
    accuracy: 'High',
    description: 'Models relationships between assets, sectors, and economic indicators as interconnected nodes.',
  },
];

export function AlgorithmList() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Prediction Algorithms</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px] pr-4">
          <div className="space-y-4">
            {algorithms.map((algo) => (
              <Card key={algo.name} className="p-4 hover:bg-muted/50 cursor-pointer">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-semibold">{algo.name}</h3>
                    <p className="text-sm text-muted-foreground">{algo.description}</p>
                  </div>
                  <Badge variant={algo.accuracy === 'Very High' ? 'default' : 'secondary'}>
                    {algo.accuracy}
                  </Badge>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  <Badge variant="outline">{algo.type}</Badge>
                  {algo.timeframes.map((timeframe) => (
                    <Badge key={timeframe} variant="outline" className="bg-background">
                      {timeframe}
                    </Badge>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
