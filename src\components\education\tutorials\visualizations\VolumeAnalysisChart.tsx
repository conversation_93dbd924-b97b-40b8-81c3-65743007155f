
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ComposedChart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

const volumeData = [
  { day: 'Mon', price: 100, volume: 1200, trend: 'neutral' },
  { day: 'Tue', price: 105, volume: 2800, trend: 'bullish' },
  { day: 'Wed', price: 108, volume: 3200, trend: 'bullish' },
  { day: 'Thu', price: 103, volume: 2100, trend: 'bearish' },
  { day: 'Fri', price: 106, volume: 2600, trend: 'bullish' },
  { day: 'Sat', price: 102, volume: 800, trend: 'bearish' },
  { day: 'Sun', price: 109, volume: 3800, trend: 'bullish' }
];

export const VolumeAnalysisChart = React.memo(function VolumeAnalysisChart() {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-semibold">{`${label}`}</p>
          <p className="text-blue-600">{`Price: $${data.price}`}</p>
          <p className="text-purple-600">{`Volume: ${data.volume.toLocaleString()}`}</p>
          <p className={`capitalize ${
            data.trend === 'bullish' ? 'text-green-600' : 
            data.trend === 'bearish' ? 'text-red-600' : 'text-gray-600'
          }`}>
            {`Trend: ${data.trend}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Volume & Price Relationship</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={volumeData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="day" 
                stroke="#666"
                fontSize={12}
              />
              <YAxis 
                yAxisId="price"
                orientation="left"
                stroke="#3b82f6"
                fontSize={12}
              />
              <YAxis 
                yAxisId="volume"
                orientation="right"
                stroke="#8b5cf6"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              <Bar 
                yAxisId="volume"
                dataKey="volume" 
                fill="#8b5cf6"
                fillOpacity={0.6}
                name="Volume"
              />
              <Line 
                yAxisId="price"
                type="monotone" 
                dataKey="price" 
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                name="Price ($)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-3 bg-green-50 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-1">Volume Confirmation</h4>
            <p className="text-sm text-green-700">
              High volume with price increase confirms bullish momentum
            </p>
          </div>
          
          <div className="p-3 bg-red-50 rounded-lg">
            <h4 className="font-semibold text-red-800 mb-1">Volume Divergence</h4>
            <p className="text-sm text-red-700">
              Price up with low volume may signal weakness
            </p>
          </div>
          
          <div className="p-3 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-1">Volume Spikes</h4>
            <p className="text-sm text-blue-700">
              Sudden volume increases often signal important events
            </p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold mb-2">Volume Analysis Rules:</h4>
          <ul className="text-sm space-y-1">
            <li>• High volume + price up = Strong bullish signal</li>
            <li>• High volume + price down = Strong bearish signal</li>
            <li>• Low volume + price movement = Weak signal</li>
            <li>• Volume should lead price for sustained moves</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
});
